{"chat_direction_left_to_right": "왼쪽에서 오른쪽으로의 채팅 방향입니다. 내용이 비어 있었습니다.", "chat_direction_right_to_left": "오른쪽에서 왼쪽으로의 채팅 방향입니다. 내용이 비어 있었습니다.", "com_a11y_ai_composing": "AI가 응답을 작성 중입니다", "com_a11y_end": "AI의 답변이 끝났습니다.", "com_a11y_start": "AI가 응답 중입니다", "com_agents_allow_editing": "다른 사용자가 내 에이전트를 수정할 수 있도록 허용", "com_agents_by_librechat": "LibreChat 제공", "com_agents_code_interpreter": "활성화하면 에이전트가 LibreChat 코드 인터프리터 API를 사용하여 파일 처리를 포함한 생성된 코드를 안전하게 실행할 수 있습니다. 유효한 API 키가 필요합니다.", "com_agents_code_interpreter_title": "코드 인터프리터 API", "com_agents_create_error": "에이전트 생성 중 오류가 발생했습니다", "com_agents_description_placeholder": "선택 사항: 여기에 에이전트를 설명하세요", "com_agents_enable_file_search": "파일 검색 활성화", "com_agents_file_context": "파일 컨텍스트 (OCR)", "com_agents_file_context_disabled": "파일 컨텍스트를 위해 파일을 업로드하기 전에, 에이전트가 먼저 생성되어야 합니다.", "com_agents_file_context_info": "컨텍스트(Context)로 업로드 된 파일은 OCR을 사용하여 텍스트를 추출하고, 이 텍스트는 에이전트의 지시사항에 추가됩니다. 문서, 텍스트가 포함된 이미지, 또는 파일의 전체 내용이 필요한 PDF에 이상적입니다.", "com_agents_file_search_disabled": "파일 검색을 위해 파일을 업로드하기 전에 에이전트를 먼저 생성해야 합니다", "com_agents_file_search_info": "활성화하면 에이전트가 아래 나열된 파일명들을 정확히 인식하여 해당 파일들에서 관련 내용을 검색할 수 있습니다.", "com_agents_instructions_placeholder": "에이전트가 사용하는 시스템 지침", "com_agents_mcp_description_placeholder": "역할을 간단히 소개해주세요", "com_agents_mcp_icon_size": "최소 크기 128 x 128 픽셀", "com_agents_mcp_info": "에이전트가 작업을 수행하고 외부 서비스와 연동할 수 있도록 MCP 서버를 추가하세요", "com_agents_mcp_name_placeholder": "커스텀 툴", "com_agents_missing_provider_model": "에이전트를 생성하기 전에 제공업체와 모델을 선택해 주세요", "com_agents_name_placeholder": "선택 사항: 에이전트의 이름", "com_agents_no_access": "이 에이전트를 수정할 권한이 없습니다", "com_agents_not_available": "에이전트를 사용할 수 없음", "com_agents_search_info": "활성화하면 에이전트가 최신 정보를 검색할 수 있도록 허용합니다. 유효한 API 키가 필요합니다.", "com_agents_search_name": "이름으로 에이전트 검색", "com_agents_update_error": "에이전트 업데이트 중 오류가 발생했습니다", "com_assistants_action_attempt": "{{0}}에게 대화 시도", "com_assistants_actions": "작업", "com_assistants_actions_disabled": "어시스턴트를 만들어야 작업을 추가할 수 있습니다.", "com_assistants_actions_info": "어시스턴트가 API를 통해 정보를 검색하거나 작업을 수행할 수 있게 해줍니다.", "com_assistants_add_actions": "작업 추가", "com_assistants_add_tools": "도구 추가", "com_assistants_allow_sites_you_trust": "신뢰하는 사이트만 허용하세요.", "com_assistants_append_date": "현재 날짜와 시간 추가", "com_assistants_append_date_tooltip": "활성화하면 현재 클라이언트의 날짜와 시간이 어시스턴트의 시스템 지침에 추가됩니다.", "com_assistants_attempt_info": "에이전트가 다음을 보내려고 합니다:", "com_assistants_available_actions": "사용 가능한 작업", "com_assistants_capabilities": "기능", "com_assistants_code_interpreter": "코드 인터프리터", "com_assistants_code_interpreter_files": "코드 인터프리터에서만 다음 파일을 사용할 수 있습니다:", "com_assistants_code_interpreter_info": "코드 인터프리터를 사용하면 어시스턴트가 코드를 작성하고 실행할 수 있습니다. 이 도구는 다양한 데이터와 형식의 파일을 처리하고, 그래프와 같은 파일을 생성할 수 있습니다.", "com_assistants_completed_action": "{{0}}과 대화했습니다", "com_assistants_completed_function": "{{0}}을(를) 실행했습니다", "com_assistants_conversation_starters": "대화 시작하기", "com_assistants_conversation_starters_placeholder": "대화를 시작할 문구를 입력하세요", "com_assistants_create_error": "어시스턴트 생성 중 오류가 발생했습니다.", "com_assistants_create_success": "계정이 성공적으로 생성되었습니다", "com_assistants_delete_actions_error": "작업 삭제 중 오류가 발생했습니다", "com_assistants_delete_actions_success": "어시스턴트에서 작업이 성공적으로 삭제되었습니다", "com_assistants_description_placeholder": "옵션: 여기에 어시스턴트를 설명하세요", "com_assistants_domain_info": "어시스턴트가 {{0}}에게 이 정보를 보냈습니다", "com_assistants_file_search": "파일 검색", "com_assistants_file_search_info": "파일 검색을 위한 벡터 저장소 연결은 아직 지원되지 않습니다. Provider Playground에서 연결하거나 스레드 기반으로 메시지에 파일을 첨부하여 파일 검색을 할 수 있습니다.", "com_assistants_function_use": "어시스턴트는 {{0}}을(를) 사용했습니다.", "com_assistants_image_vision": "이미지 인식", "com_assistants_instructions_placeholder": "보조 지침은 보조가 사용하는 시스템 지침입니다.", "com_assistants_knowledge": "지식", "com_assistants_knowledge_disabled": "지식으로 파일을 업로드하기 전에 Assistant를 생성하고 Code Interpreter 또는 Retrieval을 활성화한 후 저장해야 합니다.", "com_assistants_knowledge_info": "Knowledge에 파일을 업로드하면 어시스턴트와의 대화에서 파일 내용이 포함될 수 있습니다.", "com_assistants_max_starters_reached": "대화 시작 문구 최대 개수에 도달했습니다", "com_assistants_name_placeholder": "선택 사항: 어시스턴트의 이름", "com_assistants_non_retrieval_model": "이 모델에서는 파일 검색 기능을 사용할 수 없습니다. 다른 모델을 선택하세요.", "com_assistants_retrieval": "검색", "com_assistants_running_action": "작업 진행 중", "com_assistants_running_var": "{{0}} 실행 중", "com_assistants_search_name": "이름으로 도우미 검색", "com_assistants_update_actions_error": "작업을 생성하거나 업데이트하는 중에 오류가 발생했습니다.", "com_assistants_update_actions_success": "액션이 성공적으로 생성 또는 업데이트되었습니다", "com_assistants_update_error": "어시스턴트 업데이트 중 오류가 발생했습니다.", "com_assistants_update_success": "업데이트 성공", "com_auth_already_have_account": "이미 계정이 있으신가요?", "com_auth_apple_login": "애플로 로그인", "com_auth_back_to_login": "로그인 화면으로 돌아가기", "com_auth_click": "클릭", "com_auth_click_here": "여기를 클릭하세요", "com_auth_continue": "계속", "com_auth_create_account": "계정 만들기", "com_auth_discord_login": "Discord로 로그인", "com_auth_email": "이메일", "com_auth_email_address": "이메일 주소", "com_auth_email_max_length": "이메일은 120자를 넘을 수 없습니다", "com_auth_email_min_length": "이메일은 최소 6자 이상이어야 합니다", "com_auth_email_pattern": "유효한 이메일 주소를 입력하세요", "com_auth_email_required": "이메일은 필수입니다", "com_auth_email_resend_link": "이메일 다시 보내기", "com_auth_email_resent_failed": "인증 이메일 재전송 실패", "com_auth_email_resent_success": "인증 이메일이 성공적으로 재전송되었습니다", "com_auth_email_verification_failed": "이메일 인증에 실패했습니다", "com_auth_email_verification_failed_token_missing": "인증 실패: 토큰이 없습니다", "com_auth_email_verification_in_progress": "이메일 주소 확인 중입니다. 잠시만 기다려 주세요.", "com_auth_email_verification_invalid": "이메일 인증이 유효하지 않습니다", "com_auth_email_verification_redirecting": "{{0}}초 후 이동합니다...", "com_auth_email_verification_resend_prompt": "이메일을 받지 못하셨나요?", "com_auth_email_verification_success": "이메일 인증 완료", "com_auth_email_verifying_ellipsis": "인증 중...", "com_auth_error_create": "계정을 등록하는 중에 오류가 발생했습니다. 다시 시도하세요.", "com_auth_error_invalid_reset_token": "이 비밀번호 재설정 토큰은 더 이상 유효하지 않습니다.", "com_auth_error_login": "제공된 정보로 로그인할 수 없습니다. 자격 증명을 확인하고 다시 시도하세요.", "com_auth_error_login_ban": "서비스 이용 규정을 위반하여 계정이 일시적으로 제한되었습니다.", "com_auth_error_login_rl": "짧은 시간 동안 너무 많은 로그인 시도가 있었습니다. 잠시 후 다시 시도해 주세요.", "com_auth_error_login_server": "내부 서버 오류가 발생했습니다. 잠시 기다렸다가 다시 시도해 주세요.", "com_auth_error_login_unverified": "계정이 아직 인증되지 않았습니다. 이메일에서 인증 링크를 확인해 주세요.", "com_auth_facebook_login": "Facebook으로 로그인", "com_auth_full_name": "이름", "com_auth_github_login": "Github으로 로그인", "com_auth_google_login": "Google로 로그인", "com_auth_here": "여기", "com_auth_login": "로그인", "com_auth_login_with_new_password": "새로운 비밀번호로 로그인할 수 있습니다.", "com_auth_name_max_length": "이름은 80자를 초과할 수 없습니다", "com_auth_name_min_length": "이름은 최소 3자 이상이어야 합니다", "com_auth_name_required": "이름은 필수입니다", "com_auth_no_account": "계정이 없으신가요?", "com_auth_password": "비밀번호", "com_auth_password_confirm": "비밀번호 확인", "com_auth_password_forgot": "비밀번호를 잊으셨나요?", "com_auth_password_max_length": "비밀번호는 128자를 넘을 수 없습니다", "com_auth_password_min_length": "비밀번호는 최소 8자 이상이어야 합니다", "com_auth_password_not_match": "비밀번호가 일치하지 않습니다", "com_auth_password_required": "비밀번호는 필수입니다", "com_auth_registration_success_generic": "이메일 주소를 확인하기 위해 메일함을 확인해 주세요.", "com_auth_registration_success_insecure": "등록이 완료되었습니다.", "com_auth_reset_password": "비밀번호 재설정", "com_auth_reset_password_if_email_exists": "해당 이메일 주소로 등록된 계정이 있다면, 비밀번호 재설정 안내 메일을 발송했습니다. 스팸 폴더도 확인해 주세요.", "com_auth_reset_password_link_sent": "이메일 전송", "com_auth_reset_password_success": "비밀번호 재설정 성공", "com_auth_sign_in": "로그인", "com_auth_sign_up": "가입하기", "com_auth_submit_registration": "등록하기", "com_auth_to_reset_your_password": "비밀번호를 재설정하려면", "com_auth_to_try_again": "다시 시도하세요.", "com_auth_two_factor": "1회용 비밀번호 애플리케이션에서 코드를 확인하세요", "com_auth_username": "사용자명", "com_auth_username_max_length": "사용자명은 20자를 초과할 수 없습니다", "com_auth_username_min_length": "사용자명은 최소 2자 이상이어야 합니다", "com_auth_verify_your_identity": "본인 인증하기", "com_auth_welcome_back": "다시 오신 것을 환영합니다", "com_citation_more_details": "{{label}}에 대한 자세한 정보", "com_citation_source": "출처", "com_click_to_download": "(다운로드하려면 클릭하세요)", "com_download_expired": "다운로드가 만료되었습니다", "com_download_expires": "(다운로드하려면 클릭하세요 - {{0}} 후 만료)", "com_endpoint": "엔드포인트", "com_endpoint_agent": "에이전트", "com_endpoint_agent_model": "에이전트 모델 (권장: GPT-3.5)", "com_endpoint_agent_placeholder": "에이전트를 선택해 주세요", "com_endpoint_ai": "인공지능", "com_endpoint_anthropic_maxoutputtokens": "응답에서 생성할 수 있는 최대 토큰 수입니다. 짧은 응답에는 낮은 값을, 긴 응답에는 높은 값을 지정하세요.", "com_endpoint_anthropic_prompt_cache": "프롬프트 캐싱을 사용하면 API 호출 간에 큰 맥락이나 지시사항을 재사용할 수 있어 비용과 지연 시간을 줄일 수 있습니다", "com_endpoint_anthropic_temp": "0에서 1 사이의 값으로, 분석/다중 선택에는 0에 가까운 값을 사용하고, 창의적이고 생성적인 작업에는 1에 가까운 값을 사용하세요. 이 값을 변경하거나 Top P 중 하나만 변경하는 것을 권장합니다.", "com_endpoint_anthropic_thinking": "지원되는 Claude 모델(3.7 Sonnet)에 대한 내부 추론 기능을 활성화합니다. 참고: 사고 예산(Thinking Budget)이 활성화되어 있고, 해당 값은 최대 출력 토큰(Max Output Tokens)보단 낮아야 합니다.", "com_endpoint_anthropic_thinking_budget": "Claude의 내부 추론에 사용할 수 있는 최대 토큰 수를 결정합니다. 큰 예산은 복잡한 문제에 대해 더 철저한 분석을 가능하게 하여 응답 품질을 개선할 수 있지만, 32K 이상 범위에서는 Claude가 할당된 전체 예산을 모두 사용하지 않을 수도 있습니다. 이 설정은 \"최대 출력 토큰\"보다 낮아야 합니다.", "com_endpoint_anthropic_topk": "Top-k는 모델이 출력에 사용할 토큰을 선택하는 방식을 변경합니다. top-k가 1인 경우 모델의 어휘 중 가장 확률이 높은 토큰이 선택됩니다(greedy decoding). top-k가 3인 경우 다음 토큰은 가장 확률이 높은 3개의 토큰 중에서 선택됩니다(temperature 사용).", "com_endpoint_anthropic_topp": "Top-p는 모델이 출력에 사용할 토큰을 선택하는 방식을 변경합니다. 토큰은 가장 높은 확률부터 가장 낮은 확률까지 선택됩니다. 선택된 토큰의 확률의 합이 top-p 값과 같아질 때까지 선택됩니다.", "com_endpoint_assistant": "어시스턴트", "com_endpoint_assistant_model": "에이전트 모델", "com_endpoint_assistant_placeholder": "오른쪽 사이드 패널에서 에이전트를 선택하세요", "com_endpoint_completion": "완료", "com_endpoint_completion_model": "완료 모델 (권장: GPT-4)", "com_endpoint_config_click_here": "여기를 클릭하세요", "com_endpoint_config_google_api_info": "Gemini에서 Generative Language API 키를 얻으려면", "com_endpoint_config_google_api_key": "Google API 키", "com_endpoint_config_google_cloud_platform": "Google Cloud Platform 엔드포인트 설정", "com_endpoint_config_google_gemini_api": "Gemini API 설정", "com_endpoint_config_google_service_key": "Google 서비스 계정 키", "com_endpoint_config_key": "API 키 설정", "com_endpoint_config_key_encryption": "키는 암호화되어 저장되며, 만료 시간에 삭제됩니다", "com_endpoint_config_key_for": "API 키 설정: ", "com_endpoint_config_key_google_need_to": "API 키를 설정해야 합니다", "com_endpoint_config_key_google_service_account": "서비스 계정 생성", "com_endpoint_config_key_google_vertex_ai": "Vertex AI 사용", "com_endpoint_config_key_google_vertex_api": "Google Cloud에서 제공하는 API", "com_endpoint_config_key_google_vertex_api_role": "'Vertex AI 사용자' 역할을 부여하려면 반드시 '생성 및 계속'을 클릭하세요. 마지막으로 여기에 가져올 JSON 키를 생성하세요.", "com_endpoint_config_key_import_json_key": "서비스 계정 JSON 키 가져오기", "com_endpoint_config_key_import_json_key_invalid": "유효하지 않은 서비스 계정 JSON 키입니다. 올바른 파일을 가져왔는지 확인하세요", "com_endpoint_config_key_import_json_key_success": "서비스 계정 JSON 키 가져오기 성공", "com_endpoint_config_key_name": "키", "com_endpoint_config_key_never_expires": "키가 만료되지 않습니다", "com_endpoint_config_placeholder": "헤더 메뉴에서 키를 설정하여 채팅하세요.", "com_endpoint_config_value": "값 입력", "com_endpoint_context": "컨텍스트", "com_endpoint_context_info": "컨텍스트로 사용할 수 있는 최대 토큰 수입니다. 요청마다 보내는 토큰 수를 제어하는 데 사용할 수 있습니다. 지정하지 않으면 알려진 모델의 컨텍스트 크기를 기반으로 시스템 기본값을 사용합니다. 더 높은 값을 설정하면 오류가 발생하거나 토큰 비용이 더 높아질 수 있습니다.", "com_endpoint_context_tokens": "최대 컨텍스트 토큰 수", "com_endpoint_custom_name": "사용자 정의 이름", "com_endpoint_default": "기본값", "com_endpoint_default_blank": "기본값: 공백", "com_endpoint_default_empty": "기본값: 비어 있음", "com_endpoint_default_with_num": "기본값: {{0}}", "com_endpoint_deprecated": "단축됨", "com_endpoint_deprecated_info": "이 엔드포인트는 단축되었으며 향후 버전에서 제거될 수 있습니다. 대신 에이전트 엔드포인트를 사용하세요.", "com_endpoint_deprecated_info_a11y": "이 플러그인 엔드포인트는 단축되었으며 향후 버전에서 제거될 수 있습니다. 대신 에이전트 엔드포인트를 사용하세요.", "com_endpoint_examples": " 프리셋", "com_endpoint_export": "내보내기", "com_endpoint_export_share": "내보내기/공유", "com_endpoint_frequency_penalty": "빈도 패널티", "com_endpoint_func_hover": "플러그인을 OpenAI 함수로 사용할 수 있도록 합니다.", "com_endpoint_google_custom_name_placeholder": "Google에 대한 사용자 정의 이름 설정", "com_endpoint_google_maxoutputtokens": "응답에서 생성할 수 있는 최대 토큰 수입니다. 짧은 응답에는 낮은 값을, 긴 응답에는 높은 값을 지정하세요.", "com_endpoint_google_temp": "높은 값 = 더 무작위, 낮은 값 = 더 집중적이고 결정적입니다. 이 값을 변경하거나 Top P 중 하나만 변경하는 것을 권장합니다.", "com_endpoint_google_topk": "Top-k는 모델이 출력에 사용할 토큰을 선택하는 방식을 변경합니다. top-k가 1인 경우 모델의 어휘 중 가장 확률이 높은 토큰이 선택됩니다(greedy decoding). top-k가 3인 경우 다음 토큰은 가장 확률이 높은 3개의 토큰 중에서 선택됩니다(temperature 사용).", "com_endpoint_google_topp": "Top-p는 모델이 출력에 사용할 토큰을 선택하는 방식을 변경합니다. 토큰은 가장 높은 확률부터 가장 낮은 확률까지 선택됩니다. 선택된 토큰의 확률의 합이 top-p 값과 같아질 때까지 선택됩니다.", "com_endpoint_instructions_assistants": "에이전트 지침 재정의", "com_endpoint_instructions_assistants_placeholder": "어시스턴트의 지침을 재정의합니다. 이를 통해 실행마다 동작을 수정할 수 있습니다.", "com_endpoint_max_output_tokens": "최대 출력 토큰 수", "com_endpoint_message": "메시지", "com_endpoint_message_new": "메시지 {{0}}", "com_endpoint_message_not_appendable": "메시지를 수정하거나 다시 생성하세요.", "com_endpoint_my_preset": "내 프리셋", "com_endpoint_no_presets": "아직 프리셋이 없습니다", "com_endpoint_open_menu": "메뉴 열기", "com_endpoint_openai_custom_name_placeholder": "ChatGPT에 대한 사용자 정의 이름을 설정하세요.", "com_endpoint_openai_detail": "비전 요청의 해상도입니다. \"낮음\"은 저렴하고 빠르며, \"높음\"은 더 상세하지만 비용이 많이 듭니다. \"자동\"은 이미지 해상도에 따라 두 가지 중 하나를 자동으로 선택합니다.", "com_endpoint_openai_freq": "텍스트에서 토큰의 빈도수에 따라 새로운 토큰에 패널티를 부여합니다. 이전에 나온 텍스트의 빈도수에 따라 새로운 토큰의 확률이 감소하여 동일한 문장을 반복할 가능성을 줄입니다.", "com_endpoint_openai_max": "생성할 최대 토큰 수입니다. 입력 토큰과 생성된 토큰의 총 길이는 모델의 컨텍스트 길이로 제한됩니다.", "com_endpoint_openai_max_tokens": "선택적 `max_tokens` 필드로, 채팅 완성에서 생성할 수 있는 최대 토큰 수를 나타냅니다. 입력 토큰과 생성된 토큰의 총 길이는 모델의 컨텍스트 길이로 제한됩니다. 이 숫자가 최대 컨텍스트 토큰 수를 초과하면 오류가 발생할 수 있습니다.", "com_endpoint_openai_pres": "텍스트에서 토큰이 나타나는지 여부에 따라 새로운 토큰에 패널티를 부여합니다. 이전에 나온 텍스트에 나타나는 토큰에 대한 패널티를 증가시켜 새로운 주제에 대해 이야기할 가능성을 높입니다.", "com_endpoint_openai_prompt_prefix_placeholder": "시스템 메시지에 포함할 사용자 정의 지시사항을 설정하세요. 기본값: 없음", "com_endpoint_openai_reasoning_effort": "o1 및 o3 모델 전용: 추론 모델의 추론 노력(reasoning effort)을 제한합니다. 추론 노력을 줄이면 응답 속도가 빨라지고, 응답에서 사용되는 추론 관련 토큰 수가 줄어들 수 있습니다.", "com_endpoint_openai_resend": "이전에 첨부한 모든 이미지를 다시 전송합니다. 참고: 이렇게 하면 토큰 비용이 크게 증가할 수 있으며, 많은 이미지를 첨부하면 오류가 발생할 수 있습니다.", "com_endpoint_openai_resend_files": "이전에 첨부한 모든 파일을 다시 보내세요. 참고: 이렇게 하면 토큰 비용이 증가하고 많은 첨부 파일로 인해 오류가 발생할 수 있습니다.", "com_endpoint_openai_stop": "API가 추가 토큰 생성을 중지할 최대 4개의 시퀀스입니다.", "com_endpoint_openai_temp": "높은 값 = 더 무작위, 낮은 값 = 더 집중적이고 결정적입니다. 이 값을 변경하거나 Top P 중 하나만 변경하는 것을 권장합니다.", "com_endpoint_openai_topp": "온도를 사용한 샘플링 대신, top_p 확률 질량을 고려하는 nucleus 샘플링입니다. 따라서 0.1은 상위 10% 확률 질량을 구성하는 토큰만 고려합니다. 이 값을 변경하거나 온도를 변경하는 것을 권장하지만, 둘 다 변경하지는 마세요.", "com_endpoint_output": "출력", "com_endpoint_plug_image_detail": "이미지 상세 정보", "com_endpoint_plug_resend_files": "파일 재전송", "com_endpoint_plug_set_custom_instructions_for_gpt_placeholder": "시스템 메시지에 포함할 사용자 정의 지시사항을 설정하세요. 기본값: 없음", "com_endpoint_plug_skip_completion": "완료 단계 건너뛰기", "com_endpoint_plug_use_functions": "함수 사용", "com_endpoint_presence_penalty": "존재 패널티", "com_endpoint_preset": "프리셋", "com_endpoint_preset_custom_name_placeholder": "여기에 내용을 입력하세요. 비어 있었습니다.", "com_endpoint_preset_default": "이제 기본 프리셋입니다.", "com_endpoint_preset_default_item": "기본값:", "com_endpoint_preset_default_none": "기본 프리셋이 설정되지 않았습니다.", "com_endpoint_preset_default_removed": "더 이상 기본 프리셋이 아닙니다", "com_endpoint_preset_delete_confirm": "이 프리셋을 삭제하시겠습니까?", "com_endpoint_preset_delete_error": "프리셋을 삭제하는 중에 오류가 발생했습니다. 다시 시도해 주세요.", "com_endpoint_preset_import": "프리셋 가져왔습니다!", "com_endpoint_preset_import_error": "프리셋을 가져오는 중에 오류가 발생했습니다. 다시 시도해주세요.", "com_endpoint_preset_name": "프리셋 이름", "com_endpoint_preset_save_error": "프리셋을 저장하는 중에 오류가 발생했습니다. 다시 시도해 주세요.", "com_endpoint_preset_selected": "프리셋 활성화됨", "com_endpoint_preset_selected_title": "활성화됨", "com_endpoint_preset_title": "프리셋", "com_endpoint_presets": "프리셋", "com_endpoint_presets_clear_warning": "모든 프리셋을 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.", "com_endpoint_prompt_cache": "프롬프트 캐싱 사용", "com_endpoint_prompt_prefix": "프롬프트 접두사", "com_endpoint_prompt_prefix_assistants": "추가 지시사항", "com_endpoint_prompt_prefix_assistants_placeholder": "추가 지시사항 또는 컨텍스트를 Assistant의 기본 지시사항에 추가합니다. 비어 있으면 무시됩니다.", "com_endpoint_prompt_prefix_placeholder": "사용자 정의 지시사항 또는 컨텍스트를 설정하세요. 비어 있으면 무시됩니다.", "com_endpoint_reasoning_effort": "추론 노력", "com_endpoint_save_as_preset": "프리셋으로 저장", "com_endpoint_search": "이름으로 엔드포인트 검색", "com_endpoint_search_endpoint_models": "{{0}} 모델 검색중...", "com_endpoint_search_models": "모델 검색...", "com_endpoint_search_var": "{{0}} 검색...", "com_endpoint_set_custom_name": "프리셋을 쉽게 찾을 수 있도록 사용자 정의 이름을 설정하세요", "com_endpoint_skip_hover": "완료 단계를 건너뛰도록 합니다. 최종 답변과 생성된 단계를 검토하는 단계입니다.", "com_endpoint_stop": "중지 시퀀스", "com_endpoint_stop_placeholder": "Enter 키를 눌러 값을 구분하세요", "com_endpoint_temperature": "온도", "com_endpoint_thinking": "생각중", "com_endpoint_thinking_budget": "사고 예산", "com_endpoint_top_k": "Top K", "com_endpoint_top_p": "Top P", "com_endpoint_use_active_assistant": "활성 에이전트 사용", "com_error_expired_user_key": "{{0}}에 대한 키가 {{1}}에 만료되었습니다. 새 키를 제공하고 다시 시도해주세요.", "com_error_files_dupe": "중복된 파일이 감지되었습니다", "com_error_files_empty": "빈 파일은 허용되지 않습니다", "com_error_files_process": "파일 처리 중 오류가 발생했습니다", "com_error_files_unsupported_capability": "이 파일 형식을 지원하는 기능이 활성화되어 있지 않습니다", "com_error_files_upload": "파일 업로드 중 오류가 발생했습니다", "com_error_files_upload_canceled": "파일 업로드가 취소되었습니다. 참고: 업로드 처리가 아직 진행 중일 수 있으며 수동으로 삭제해야 할 수 있습니다.", "com_error_files_validation": "파일 유효성 검사 중 오류가 발생했습니다", "com_error_input_length": "최신 메시지의 토큰 수가 너무 많아 토큰 제한을 초과했거나, 토큰 제한 관련 파라미터가 잘못 설정되어 있어 컨텍스트 창에 부정적인 영향을 미치고 있습니다. 자세한 정보: {{0}}. 메시지를 줄이거나, 대화 파라미터에서 최대 컨텍스트 크기를 조정하거나, 대화를 포크(fork)하여 계속 진행해 주세요.", "com_error_invalid_agent_provider": "\"{{0}}\" 제공자는 에이전트와 함께 사용할 수 없습니다. 에이전트 설정으로 이동하여 현재 사용 가능한 제공자를 선택하세요.", "com_error_invalid_user_key": "제공된 키가 유효하지 않습니다. 키를 제공하고 다시 시도해주세요.", "com_error_moderation": "제출된 내용이 커뮤니티 가이드라인에 부합하지 않는다고 판단되어 모더레이션 시스템에 의해 차단되었습니다. 해당 주제로는 진행할 수 없습니다. 다른 질문이나 탐구하고 싶은 주제가 있다면 메시지를 수정하거나 새 대화를 시작해 주세요.", "com_error_no_base_url": "기본 URL이 없습니다. URL을 제공한 후 다시 시도해 주세요.", "com_error_no_user_key": "키를 찾을 수 없습니다. 키를 제공하고 다시 시도해주세요.", "com_files_filter": "파일 필터링...", "com_files_no_results": "결과가 없습니다.", "com_files_number_selected": "{{0}}개의 파일({{1}}개 중)이 선택되었습니다", "com_files_table": "내용이 비어 있었습니다.", "com_generated_files": "생성된 파일:", "com_hide_examples": "예시 숨기기", "com_nav_2fa": "이단계 인증 (2FA)", "com_nav_account_settings": "계정 설정", "com_nav_always_make_prod": "항상 새 버전을 프로덕션으로 설정", "com_nav_archive_created_at": "생성 날짜", "com_nav_archive_name": "이름", "com_nav_archived_chats": "아카이브된 채팅", "com_nav_at_command": "@ 명령어", "com_nav_at_command_description": "엔드포인트, 모델, 프리셋 등을 전환하는 \"@\" 명령어 토글", "com_nav_audio_play_error": "오디오 재생 오류: {{0}}", "com_nav_audio_process_error": "오디오 처리 오류: {{0}}", "com_nav_auto_scroll": "채팅 열렸을 때 최신 메시지로 자동 스크롤", "com_nav_auto_send_prompts": "프롬프트 자동 전송", "com_nav_auto_send_text": "자동 메시지 전송", "com_nav_auto_send_text_disabled": "자동 전송 비활성화는 -1로 설정", "com_nav_auto_transcribe_audio": "오디오 자동 변환", "com_nav_automatic_playback": "최신 메시지 자동 재생", "com_nav_balance": "잔고", "com_nav_browser": "브라우저", "com_nav_center_chat_input": "환영 화면에서 채팅 입력 중앙 정렬", "com_nav_change_picture": "프로필 사진 변경", "com_nav_chat_commands": "채팅 명령어", "com_nav_chat_commands_info": "이 명령어들은 메시지 시작 부분에 특정 문자를 입력하면 활성화됩니다. 각 명령어는 지정된 접두사로 실행됩니다. 메시지 작성 시 이러한 문자들을 자주 사용하신다면 명령어 기능을 비활성화할 수 있습니다.", "com_nav_chat_direction": "채팅 표시 방향", "com_nav_clear_all_chats": "모든 채팅 지우기", "com_nav_clear_cache_confirm_message": "캐시를 지우시겠습니까?", "com_nav_clear_conversation": "대화 지우기", "com_nav_clear_conversation_confirm_message": "모든 대화를 지우시겠습니까? 이 작업은 되돌릴 수 없습니다.", "com_nav_close_sidebar": "사이드바 닫기", "com_nav_commands": "명령어", "com_nav_confirm_clear": "지우기 확인", "com_nav_conversation_mode": "대화 모드", "com_nav_convo_menu_options": "대화 메뉴 옵션", "com_nav_db_sensitivity": "데시벨 감도", "com_nav_delete_account": "계정 삭제", "com_nav_delete_account_button": "내 계정 영구 삭제", "com_nav_delete_account_confirm": "계정 삭제 - 정말 삭제하시겠습니까?", "com_nav_delete_account_email_placeholder": "계정 이메일을 입력해 주세요", "com_nav_delete_cache_storage": "TTS 캐시 저장소 삭제", "com_nav_delete_data_info": "모든 데이터가 삭제됩니다.", "com_nav_delete_warning": "경고: 이 작업은 계정을 영구적으로 삭제합니다.", "com_nav_enable_cache_tts": "TTS 캐시 사용", "com_nav_enable_cloud_browser_voice": "클라우드 기반 음성 사용", "com_nav_enabled": "활성화됨", "com_nav_engine": "엔진", "com_nav_enter_to_send": "엔터키를 눌러 메시지 보내기", "com_nav_export": "내보내기", "com_nav_export_all_message_branches": "모든 메시지 브랜치 내보내기", "com_nav_export_conversation": "대화 내보내기", "com_nav_export_filename": "파일 이름", "com_nav_export_filename_placeholder": "파일 이름을 설정하세요", "com_nav_export_include_endpoint_options": "엔드포인트 옵션 포함", "com_nav_export_recursive": "재귀적", "com_nav_export_recursive_or_sequential": "재귀적 또는 순차적?", "com_nav_export_type": "유형", "com_nav_external": "외부", "com_nav_font_size": "글꼴 크기", "com_nav_font_size_base": "중간", "com_nav_font_size_lg": "큰 글자", "com_nav_font_size_sm": "작게", "com_nav_font_size_xl": "아주 큰 글자", "com_nav_font_size_xs": "아주 작게", "com_nav_help_faq": "도움말 및 FAQ", "com_nav_hide_panel": "오른쪽 사이드 패널 숨기기", "com_nav_info_balance": "잔액은 사용 가능한 토큰 크레딧의 수를 나타냅니다. 토큰 크레딧은 금전적 가치로 환산되며 (예: 1000 크레딧 = 0.001달러)입니다.", "com_nav_info_code_artifacts": "채팅 옆에 실험적 코드 결과물 표시 활성화", "com_nav_info_code_artifacts_agent": "이 에이전트에 대해 코드 아티팩트 사용을 가능하게 합니다. 기본적으로 아티팩트 사용과 관련된 추가 지침이 포함되며, \"커스텀 프롬프트 모드\"가 활성화되지 않은 경우에만 적용됩니다.", "com_nav_info_custom_prompt_mode": "활성화하면 기본 아티팩트 시스템 프롬프트가 포함되지 않습니다. 이 모드에서는 아티팩트 생성을 위한 모든 지시사항을 수동으로 입력해야 합니다.", "com_nav_info_enter_to_send": "활성화되면 `Enter` 키를 눌러 메시지를 보낼 수 있습니다. 비활성화 시 Enter 키는 줄바꿈을 하며, 메시지를 보내려면 `Ctrl + Enter`를 눌러야 합니다.", "com_nav_info_fork_change_default": "'표시된 메시지만'은 선택한 메시지로 가는 직접 경로만 포함합니다. '관련 브랜치 포함'은 경로상의 브랜치도 추가합니다. '이 메시지 전후 모두 포함'은 연결된 모든 메시지와 브랜치를 포함합니다.", "com_nav_info_fork_split_target_setting": "활성화되면 선택한 동작에 따라 대상 메시지부터 대화의 최신 메시지까지 포킹이 시작됩니다.", "com_nav_info_include_shadcnui": "활성화하면 shadcn/ui 컴포넌트 사용을 위한 설명이 포함됩니다. shadcn/ui는 Radix UI와 Tailwind CSS를 기반으로 구축된 재사용 가능한 컴포넌트 모음입니다. 참고: 이 설명은 매우 상세하므로, LLM에게 올바른 import문과 컴포넌트 사용법을 안내하는 것이 중요한 경우에만 활성화하시기 바랍니다. 컴포넌트에 대한 자세한 정보는 다음 웹사이트를 참조하세요: https://ui.shadcn.com/", "com_nav_info_latex_parsing": "활성화하면 메시지의 LaTeX 코드가 수학 방정식으로 렌더링됩니다. LaTeX 렌더링이 필요하지 않은 경우 이 기능을 비활성화하면 성능이 향상될 수 있습니다.", "com_nav_info_save_badges_state": "이 기능을 활성화하면 채팅 배지의 상태를 저장합니다. 즉, 새 채팅을 생성하더라도 이전 채팅과 동일한 상태를 유지합니다. 이 옵션을 비활성화하면 새 채팅마다 배지가 기본 상태로 재설정됩니다.", "com_nav_info_save_draft": "활성화하면 채팅 양식에 입력한 텍스트와 첨부 파일이 자동으로 로컬에 임시 저장됩니다. 페이지를 새로고침하거나 다른 대화로 전환해도 이 임시 저장 내용을 계속 사용할 수 있습니다. 임시 저장 내용은 사용자의 기기에 로컬로 저장되며, 메시지를 보내면 삭제됩니다.", "com_nav_info_show_thinking": "이 기능을 활성화하면, 채팅에서 추론 드롭다운이 기본적으로 열려 있어 AI의 사고 과정을 실시간으로 볼 수 있습니다. 비활성화하면 더 깔끔하고 간결한 인터페이스를 위해 드롭다운이 기본적으로 닫힙니다.", "com_nav_info_user_name_display": "활성화하면 보내는 각 메시지 위에 사용자 이름이 표시됩니다. 비활성화하면 내 메시지 위에 \"나\"라고만 표시됩니다.", "com_nav_lang_arabic": "العربية", "com_nav_lang_auto": "자동 감지", "com_nav_lang_brazilian_portuguese": "Portuguê<PERSON>", "com_nav_lang_catalan": "카탈로니아어", "com_nav_lang_chinese": "中文", "com_nav_lang_czech": "체코어", "com_nav_lang_danish": "덴마크어", "com_nav_lang_dutch": "Nederlands", "com_nav_lang_english": "English", "com_nav_lang_estonian": "<PERSON><PERSON><PERSON> keel", "com_nav_lang_finnish": "<PERSON><PERSON>", "com_nav_lang_french": "Français ", "com_nav_lang_georgian": "ქართული", "com_nav_lang_german": "De<PERSON>ch", "com_nav_lang_hebrew": "עברית", "com_nav_lang_hungarian": "헝가리어", "com_nav_lang_indonesia": "Indonesia", "com_nav_lang_italian": "Italiano", "com_nav_lang_japanese": "日本語", "com_nav_lang_korean": "한국어", "com_nav_lang_persian": "페르시아어", "com_nav_lang_polish": "<PERSON><PERSON>", "com_nav_lang_portuguese": "Português", "com_nav_lang_russian": "Русский", "com_nav_lang_spanish": "Español", "com_nav_lang_swedish": "Svenska", "com_nav_lang_thai": "ไทย", "com_nav_lang_traditional_chinese": "繁體中文", "com_nav_lang_turkish": "Türkçe", "com_nav_lang_vietnamese": "Tiếng <PERSON>", "com_nav_language": "언어", "com_nav_latex_parsing": "메시지에서 LaTeX 구문 분석(성능에 영향을 줄 수 있음)", "com_nav_log_out": "로그아웃", "com_nav_long_audio_warning": "긴 텍스트일수록 처리 시간이 더 오래 걸립니다.", "com_nav_maximize_chat_space": "채팅창 최대화", "com_nav_modular_chat": "대화 중간에 엔드포인트 전환 허용", "com_nav_my_files": "내 파일", "com_nav_not_supported": "지원되지 않음", "com_nav_open_sidebar": "사이드바 열기", "com_nav_playback_rate": "오디오 재생 속도", "com_nav_plugin_auth_error": "이 플러그인을 인증하려는 중에 오류가 발생했습니다. 다시 시도해주세요.", "com_nav_plugin_install": "플러그인 설치", "com_nav_plugin_search": "플러그인 검색", "com_nav_plugin_store": "플러그인 스토어", "com_nav_plugin_uninstall": "플러그인 제거", "com_nav_plus_command": "명령어 추가", "com_nav_plus_command_description": "다중 응답 설정을 추가하는 ' + ' 명령 토글", "com_nav_profile_picture": "프로필 사진", "com_nav_save_badges_state": "배지 상태 저장", "com_nav_save_drafts": "초안을 로컬에 저장", "com_nav_scroll_button": "맨 끝으로 스크롤 버튼", "com_nav_search_placeholder": "메시지 검색", "com_nav_send_message": "메시지 보내기", "com_nav_setting_account": "계정", "com_nav_setting_balance": "잔액", "com_nav_setting_beta": "베타 기능", "com_nav_setting_chat": "채팅", "com_nav_setting_data": "데이터 제어", "com_nav_setting_general": "일반", "com_nav_setting_speech": "음성", "com_nav_settings": "설정", "com_nav_shared_links": "공유 링크", "com_nav_show_code": "코드 인터프리터 사용 시 항상 코드 표시", "com_nav_show_thinking": "생각 드롭다운 기본 열기", "com_nav_slash_command": "슬래시 명령어", "com_nav_slash_command_description": "키보드로 프롬프트를 선택하려면 \"/\" 명령어 토글", "com_nav_speech_to_text": "음성을 텍스트로 변환", "com_nav_stop_generating": "생성 중단", "com_nav_text_to_speech": "텍스트 음성 변환", "com_nav_theme": "테마", "com_nav_theme_dark": "다크", "com_nav_theme_light": "라이트", "com_nav_theme_system": "시스템", "com_nav_tool_dialog": "어시스턴트 도구", "com_nav_tool_dialog_agents": "에이전트 도구", "com_nav_tool_dialog_description": "Assistant를 저장해야 도구 선택이 유지됩니다.", "com_nav_tool_remove": "제거", "com_nav_tool_search": "도구 검색", "com_nav_user": "사용자", "com_nav_user_msg_markdown": "사용자 메시지를 마크다운으로 렌더링", "com_nav_user_name_display": "메시지에서 사용자 이름 표시", "com_nav_voice_select": "음성 선택", "com_show_agent_settings": "에이전트 설정 표시", "com_show_completion_settings": "완료 설정 표시", "com_show_examples": "예시 보기", "com_sidepanel_agent_builder": "에이전트 제작기", "com_sidepanel_assistant_builder": "어시스턴트 제작기", "com_sidepanel_attach_files": "파일 첨부", "com_sidepanel_conversation_tags": "북마크", "com_sidepanel_hide_panel": "패널 숨기기", "com_sidepanel_manage_files": "파일 관리", "com_sidepanel_parameters": "매개변수", "com_sources_image_alt": "검색 결과 이미지", "com_sources_more_sources": "+{{count}}개 소스", "com_sources_tab_all": "전체", "com_sources_tab_images": "이미지", "com_sources_tab_news": "뉴스", "com_sources_title": "소스", "com_ui_2fa_account_security": "이단계 인증은 계정 보안을 강화합니다", "com_ui_2fa_disable": "2FA 비활성화", "com_ui_2fa_disable_error": "이단계 인증 비활성화 중 오류가 발생했습니다", "com_ui_2fa_disabled": "2FA가 비활성화되었습니다", "com_ui_2fa_enable": "2FA 활성화", "com_ui_2fa_enabled": "2FA가 활성화되었습니다", "com_ui_2fa_generate_error": "이단계 인증 설정 생성 중 오류가 발생했습니다", "com_ui_2fa_invalid": "잘못된 이단계 인증 코드입니다", "com_ui_2fa_setup": "2FA 설정", "com_ui_2fa_verified": "이단계 인증이 성공적으로 인증되었습니다", "com_ui_accept": "동의합니다", "com_ui_action_button": "액션 버튼", "com_ui_add": "추가", "com_ui_add_mcp": "MCP 추가", "com_ui_add_mcp_server": "MCP 서버 추가", "com_ui_add_model_preset": "추가 응답을 위한 모델 또는 프리셋 추가", "com_ui_add_multi_conversation": "다중 응답 대화 추가", "com_ui_adding_details": "세부 정보 추가 중", "com_ui_admin": "관리자", "com_ui_admin_access_warning": "관리자 접근 권한을 비활성화하면 예기치 않은 UI 문제가 발생할 수 있으며 페이지 새로고침이 필요할 수 있습니다. 저장하면 librechat.yaml 설정 파일에서 모든 역할에 영향을 미치는 인터페이스 설정을 통해서만 되돌릴 수 있습니다.", "com_ui_admin_settings": "관리자 설정", "com_ui_advanced": "고급", "com_ui_advanced_settings": "고급 설정", "com_ui_agent": "에이전트", "com_ui_agent_chain": "에이전트 체인 (에이전트 조합)", "com_ui_agent_chain_info": "에이전트 시퀀스를 생성할 수 있도록 합니다. 각 에이전트는 체인 내 이전 에이전트의 출력을 참조할 수 있으며, \"에이전트 조합\" 아키텍처를 기반으로 하여 이전 출력을 보조 정보로 활용합니다.", "com_ui_agent_chain_max": "최대 {{0}}개의 에이전트에 도달했습니다.", "com_ui_agent_delete_error": "에이전트 삭제 중 오류가 발생했습니다", "com_ui_agent_deleted": "에이전트가 삭제되었습니다", "com_ui_agent_duplicate_error": "에이전트 복제 중 오류가 발생했습니다", "com_ui_agent_duplicated": "에이전트가 성공적으로 복제되었습니다", "com_ui_agent_editing_allowed": "다른 사용자가 이미 이 에이전트를 편집할 수 있습니다", "com_ui_agent_recursion_limit": "최대 에이전트 단계 수", "com_ui_agent_recursion_limit_info": "한 번의 실행에서 에이전트가 수행할 수 있는 단계 수를 제한합니다. 기본값은 25단계입니다. 한 단계는 AI API 요청 또는 도구 사용 라운드입니다. 예를 들어, 기본 도구 상호작용은 3단계입니다: 초기 요청, 도구 사용, 후속 요청.", "com_ui_agent_shared_to_all": "내용이 비어 있었습니다.", "com_ui_agent_var": "{{0}} 에이전트", "com_ui_agent_version": "버전", "com_ui_agent_version_active": "활성 버전", "com_ui_agent_version_duplicate": "중복 버전이 감지되었습니다. 이는 버전 {{versionIndex}}와 동일한 버전을 생성합니다.", "com_ui_agent_version_empty": "사용 가능한 버전이 없습니다", "com_ui_agent_version_error": "버전 정보 가져오기 오류", "com_ui_agent_version_history": "버전 기록", "com_ui_agent_version_no_agent": "에이전트가 선택되지 않았습니다. 버전 기록을 보려면 에이전트를 선택하세요.", "com_ui_agent_version_no_date": "날짜 정보를 사용할 수 없습니다", "com_ui_agent_version_restore": "복원", "com_ui_agent_version_restore_confirm": "이 버전을 복원하시겠습니까?", "com_ui_agent_version_restore_error": "버전 복원 실패", "com_ui_agent_version_restore_success": "버전이 성공적으로 복원되었습니다", "com_ui_agent_version_title": "버전 {{versionNumber}}", "com_ui_agent_version_unknown_date": "알 수 없는 날짜", "com_ui_agents": "에이전트", "com_ui_agents_allow_create": "에이전트 생성 허용", "com_ui_agents_allow_share_global": "에이전트를 모든 사용자와 공유 허용", "com_ui_agents_allow_use": "에이전트 사용 허용", "com_ui_all": "전체", "com_ui_all_proper": "모두", "com_ui_analyzing": "분석 중", "com_ui_analyzing_finished": "분석 완료", "com_ui_api_key": "API 키", "com_ui_archive": "아카이브", "com_ui_archive_delete_error": "저장된 대화 삭제 실패", "com_ui_archive_error": "대화 아카이브 실패", "com_ui_artifact_click": "클릭하여 열기", "com_ui_artifacts": "아티팩트", "com_ui_artifacts_toggle": "아티팩트 UI 표시/숨기기", "com_ui_artifacts_toggle_agent": "아티팩트 활성화", "com_ui_ascending": "오름차순", "com_ui_assistant": "어시스턴트", "com_ui_assistant_delete_error": "어시스턴트 삭제 중 오류가 발생했습니다.", "com_ui_assistant_deleted": "어시스턴트가 성공적으로 삭제되었습니다", "com_ui_assistants": "어시스턴트", "com_ui_assistants_output": "어시스턴트 출력", "com_ui_attach_error": "파일을 첨부할 수 없습니다. 대화를 생성하거나 선택하시거나 페이지를 새로고침해 보세요.", "com_ui_attach_error_openai": "어시스턴트 파일을 다른 엔드포인트에 첨부할 수 없습니다.", "com_ui_attach_error_size": "엔드포인트에 대한 파일 크기 제한을 초과했습니다.", "com_ui_attach_error_type": "엔드포인트에서 지원하지 않는 파일 형식입니다.", "com_ui_attach_remove": "파일 제거", "com_ui_attach_warn_endpoint": "호환되는 도구가 없으면 비어시스턴트 파일이 무시될 수 있습니다.", "com_ui_attachment": "첨부 파일", "com_ui_auth_type": "인증 유형", "com_ui_auth_url": "인증 URL", "com_ui_authentication": "인증", "com_ui_authentication_type": "인증 방식", "com_ui_available_tools": "사용 가능 툴", "com_ui_avatar": "프로필 사진", "com_ui_azure": "Azure", "com_ui_back_to_chat": "채팅으로 돌아가기", "com_ui_back_to_prompts": "프롬프트로 돌아가기", "com_ui_backup_codes": "백업 코드", "com_ui_backup_codes_regenerate_error": "백업 코드 재생성 중 오류 발생", "com_ui_backup_codes_regenerated": "백업 코드가 성공적으로 재생성되었습니다", "com_ui_basic": "기본", "com_ui_basic_auth_header": "기본 인증 헤더", "com_ui_bearer": "베어러", "com_ui_bookmark_delete_confirm": "이 북마크를 삭제하시겠습니까?", "com_ui_bookmarks": "북마크", "com_ui_bookmarks_add": "북마크 추가", "com_ui_bookmarks_add_to_conversation": "현재 대화에 추가", "com_ui_bookmarks_count": "개수", "com_ui_bookmarks_create_error": "북마크 생성 중 오류가 발생했습니다", "com_ui_bookmarks_create_exists": "이미 존재하는 북마크입니다", "com_ui_bookmarks_create_success": "북마크가 성공적으로 생성되었습니다", "com_ui_bookmarks_delete": "북마크 삭제", "com_ui_bookmarks_delete_error": "북마크 삭제 중 오류가 발생했습니다", "com_ui_bookmarks_delete_success": "북마크가 성공적으로 삭제되었습니다", "com_ui_bookmarks_description": "설명", "com_ui_bookmarks_edit": "북마크 수정", "com_ui_bookmarks_filter": "북마크 필터링...", "com_ui_bookmarks_new": "새 북마크", "com_ui_bookmarks_title": "제목", "com_ui_bookmarks_update_error": "북마크 업데이트 중 오류가 발생했습니다", "com_ui_bookmarks_update_success": "북마크가 성공적으로 업데이트되었습니다", "com_ui_bulk_delete_error": "공유 링크 삭제 실패", "com_ui_callback_url": "콜백 URL", "com_ui_cancel": "취소", "com_ui_cancelled": "취소됨", "com_ui_category": "카테고리", "com_ui_chat": "채팅", "com_ui_chat_history": "대화 기록", "com_ui_clear": "지우기", "com_ui_clear_all": "모두 지우기", "com_ui_client_id": "클라이언트 ID", "com_ui_client_secret": "클라이언트 비밀", "com_ui_close": "닫기", "com_ui_close_menu": "메뉴 닫기", "com_ui_code": "코드", "com_ui_collapse_chat": "채팅 접기", "com_ui_command_placeholder": "선택 사항: 프롬프트에 대한 명령어를 입력하세요. 입력하지 않으면 이름이 사용됩니다.", "com_ui_command_usage_placeholder": "명령어나 이름으로 프롬프트 선택", "com_ui_complete_setup": "설정 완료", "com_ui_confirm_action": "작업 확인", "com_ui_confirm_admin_use_change": "이 설정을 변경하면 관리자 포함 모든 사용자의 접근이 차단됩니다. 계속하시겠습니까?", "com_ui_confirm_change": "변경 확인", "com_ui_context": "맥락", "com_ui_continue": "계속", "com_ui_controls": "컨트롤", "com_ui_convo_delete_error": "대화 삭제 실패", "com_ui_copied": "복사됨", "com_ui_copied_to_clipboard": "클립보드에 복사되었습니다", "com_ui_copy_code": "코드 복사", "com_ui_copy_link": "링크 복사", "com_ui_copy_to_clipboard": "클립보드에 복사", "com_ui_create": "만들기", "com_ui_create_link": "링크 만들기", "com_ui_create_memory": "메모리 생성", "com_ui_create_prompt": "프롬프트 만들기", "com_ui_creating_image": "이미지 생성 중입니다. 잠시 기다려 주세요.", "com_ui_currently_production": "현재 프로덕션 중", "com_ui_custom": "사용자 지정", "com_ui_custom_header_name": "사용자 지정 헤더 이름", "com_ui_custom_prompt_mode": "사용자 지정 프롬프트 모드", "com_ui_dashboard": "대시보드", "com_ui_date": "날짜", "com_ui_date_april": "4월", "com_ui_date_august": "8월", "com_ui_date_december": "12월", "com_ui_date_february": "2월", "com_ui_date_january": "1월", "com_ui_date_july": "7월", "com_ui_date_june": "6월", "com_ui_date_march": "3월", "com_ui_date_may": "5월", "com_ui_date_november": "11월", "com_ui_date_october": "10월", "com_ui_date_previous_30_days": "지난 30일", "com_ui_date_previous_7_days": "지난 7일", "com_ui_date_september": "9월", "com_ui_date_today": "오늘", "com_ui_date_yesterday": "어제", "com_ui_decline": "동의하지 않습니다", "com_ui_default_post_request": "기본(POST 요청)", "com_ui_delete": "삭제", "com_ui_delete_action": "작업 삭제", "com_ui_delete_action_confirm": "이 작업을 삭제하시겠습니까?", "com_ui_delete_agent_confirm": "이 에이전트를 삭제하시겠습니까?", "com_ui_delete_assistant_confirm": "이 Assistant를 삭제하시겠습니까? 이 작업은 취소할 수 없습니다.", "com_ui_delete_confirm": "이 채팅이 삭제됩니다", "com_ui_delete_confirm_prompt_version_var": "선택한 \"{{0}}\"의 버전이 삭제됩니다. 다른 버전이 없다면 프롬프트도 함께 삭제됩니다.", "com_ui_delete_conversation": "채팅을 삭제하시겠습니까?", "com_ui_delete_mcp": "MCP 삭제", "com_ui_delete_mcp_confirm": "MCP 서버를 삭제하시겠습니까?", "com_ui_delete_mcp_error": "MCP 서버 삭제 실패", "com_ui_delete_mcp_success": "MCP 서버 삭제 완료", "com_ui_delete_memory": "메모리 삭제", "com_ui_delete_prompt": "프롬프트를 삭제하시겠습니까?", "com_ui_delete_shared_link": "공유 링크를 삭제하시겠습니까?", "com_ui_delete_tool": "도구 삭제", "com_ui_delete_tool_confirm": "이 도구를 삭제하시겠습니까?", "com_ui_deleted": "삭제 완료", "com_ui_descending": "내림차순", "com_ui_description": "설명", "com_ui_description_placeholder": "선택 사항: 프롬프트에 표시할 설명을 입력하세요", "com_ui_deselect_all": "모두 선택 해제", "com_ui_disabling": "비활성화 중...", "com_ui_download": "다운로드", "com_ui_download_artifact": "아티팩트 다운로드", "com_ui_download_backup": "백업 코드 다운로드", "com_ui_download_backup_tooltip": "계속하기 전에 백업 코드를 다운로드하세요. 인증기 기기를 잃더라도 다시 액세스할 수 있도록 필요합니다.", "com_ui_download_error": "파일 다운로드 중 오류가 발생했습니다. 파일이 삭제되었을 수 있습니다.", "com_ui_drag_drop": "내용이 비어 있었습니다.", "com_ui_dropdown_variables": "드롭다운 변수:", "com_ui_dropdown_variables_info": "프롬프트에 사용자 정의 드롭다운 메뉴 만들기: `{{변수명:옵션1|옵션2|옵션3}}`", "com_ui_duplicate": "복제", "com_ui_duplication_error": "대화를 복제하는 중 오류가 발생했습니다", "com_ui_duplication_processing": "대화 복제 중...", "com_ui_duplication_success": "대화가 성공적으로 복제되었습니다", "com_ui_edit": "편집", "com_ui_edit_editing_image": "이미지 편집 중", "com_ui_edit_mcp_server": "MCP 서버 편집", "com_ui_edit_memory": "메모리 편집", "com_ui_empty_category": "-", "com_ui_endpoint": "엔드포인트", "com_ui_endpoint_menu": "LLM 엔드포인트 메뉴", "com_ui_enter": "Enter", "com_ui_enter_api_key": "API 키 입력", "com_ui_enter_key": "키 입력", "com_ui_enter_openapi_schema": "OpenAPI 스키마를 입력하세요", "com_ui_enter_value": "값 입력", "com_ui_error": "오류", "com_ui_error_connection": "서버 연결 오류가 발생했습니다. 페이지를 새로고침해 주세요.", "com_ui_error_save_admin_settings": "관리자 설정을 저장하는 중 오류가 발생했습니다.", "com_ui_error_updating_preferences": "설정 업데이트 오류", "com_ui_examples": "예시", "com_ui_expand_chat": "채팅 확장", "com_ui_export_convo_modal": "대화 내보내기", "com_ui_feedback_more": "더보기...", "com_ui_feedback_more_information": "추가 피드백 제공", "com_ui_feedback_negative": "개선 필요", "com_ui_feedback_placeholder": "여기에 추가 피드백을 제공해주세요", "com_ui_feedback_positive": "좋아요", "com_ui_feedback_tag_accurate_reliable": "정확하고 신뢰할 수 있음", "com_ui_feedback_tag_attention_to_detail": "디테일 함", "com_ui_feedback_tag_bad_style": "표현이나 말투가 어색함", "com_ui_feedback_tag_clear_well_written": "글이 분명하고 매끄럽게 작성됨", "com_ui_feedback_tag_inaccurate": "정확하지 않거나 잘못된 응답", "com_ui_feedback_tag_missing_image": "이미지가 포함될 줄 알았음", "com_ui_feedback_tag_not_helpful": "유용한 정보가 부족함", "com_ui_feedback_tag_not_matched": "요청과 다름", "com_ui_feedback_tag_other": "다른 문제점", "com_ui_feedback_tag_unjustified_refusal": "정확한 이유 없이 거절됨", "com_ui_field_required": "이 필드는 필수입니다", "com_ui_file_size": "파일 크기", "com_ui_files": "파일", "com_ui_filter_prompts": "프롬프트 필터링", "com_ui_filter_prompts_name": "이름으로 프롬프트 필터링", "com_ui_final_touch": "마무리", "com_ui_finance": "금융", "com_ui_fork": "포크", "com_ui_fork_all_target": "여기부터 전체 포함", "com_ui_fork_branches": "관련 브랜치 포함", "com_ui_fork_change_default": "기본 포크 옵션", "com_ui_fork_default": "기본 포크 옵션 사용", "com_ui_fork_error": "대화 분기 중 오류가 발생했습니다", "com_ui_fork_from_message": "포크 옵션 선택", "com_ui_fork_info_1": "이 설정을 사용하면 원하는 동작으로 메시지를 분기할 수 있습니다.", "com_ui_fork_info_2": "\"포킹(Forking)\"은 현재 대화에서 특정 메시지를 시작/종료 지점으로 하여 새로운 대화를 생성하고, 선택한 옵션에 따라 복사본을 만드는 것을 의미합니다.", "com_ui_fork_info_3": "\"대상 메시지\"는 이 팝업이 열린 메시지 또는 \"{{0}}\"에 체크하면 대화의 최신 메시지를 의미합니다.", "com_ui_fork_info_branches": "이 옵션은 표시된 메시지와 관련 브랜치를 분기시킵니다. 즉, 대상 메시지에 이르는 직접 경로와 그 경로에 있는 브랜치를 포함합니다.", "com_ui_fork_info_button_label": "포크 대화에 대한 정보 보기", "com_ui_fork_info_remember": "이 옵션을 선택하면 향후 대화를 더 빠르게 분기할 수 있도록 선택한 옵션을 기억합니다.", "com_ui_fork_info_start": "선택 시 이 메시지부터 대화의 최신 메시지까지 위에서 선택한 동작에 따라 포크가 시작됩니다.", "com_ui_fork_info_target": "이 옵션은 대상 메시지와 그 주변 메시지를 포함하여 대상 메시지에 이르는 모든 메시지 분기를 포크합니다. 다시 말해, 표시 여부나 동일한 경로 상에 있는지 여부와 상관없이 모든 메시지 분기가 포함됩니다.", "com_ui_fork_info_visible": "이 옵션은 표시된 메시지만 분기하여 복사합니다. 즉, 대상 메시지로 가는 직접 경로만 복사하고 다른 분기는 복사하지 않습니다.", "com_ui_fork_more_details_about": "\"{{0}}\" 포크 옵션에 대한 추가 정보 및 상세 내용 보기", "com_ui_fork_more_info_options": "모든 포크 옵션과 그 동작에 대한 자세한 설명 보기", "com_ui_fork_processing": "대화 분기 중...", "com_ui_fork_remember": "기억하기", "com_ui_fork_remember_checked": "선택한 내용은 사용 후에도 기억됩니다. 설정에서 언제든 변경할 수 있습니다.", "com_ui_fork_split_target": "여기서 포크 시작", "com_ui_fork_split_target_setting": "기본적으로 대상 메시지에서 포크 시작", "com_ui_fork_success": "대화 복제 성공", "com_ui_fork_visible": "공개 메시지만 표시", "com_ui_generate_backup": "백업 코드 생성", "com_ui_generate_qrcode": "QR 코드 생성", "com_ui_generating": "생성 중...", "com_ui_generation_settings": "출력 설정", "com_ui_getting_started": "시작하기", "com_ui_global_group": "내용이 비어 있었습니다.", "com_ui_go_back": "뒤로가기", "com_ui_go_to_conversation": "대화로 이동", "com_ui_good_afternoon": "좋은 오후입니다", "com_ui_good_evening": "좋은 저녁입니다", "com_ui_good_morning": "좋은 아침입니다", "com_ui_happy_birthday": "내 첫 생일이야!", "com_ui_hide_image_details": "이미지 세부정보 숨기기", "com_ui_hide_qr": "QR 코드 숨기기", "com_ui_host": "호스트", "com_ui_icon": "아이콘", "com_ui_idea": "아이디어", "com_ui_image_created": "이미지 생성됨", "com_ui_image_details": "이미지 세부정보", "com_ui_image_edited": "이미지 편집됨", "com_ui_image_gen": "이미지 생성", "com_ui_import": "가져오기", "com_ui_import_conversation_error": "대화를 가져오는 동안 오류가 발생했습니다", "com_ui_import_conversation_file_type_error": "가져올 수 없는 파일 형식입니다", "com_ui_import_conversation_info": "JSON 파일에서 대화 가져오기", "com_ui_import_conversation_success": "대화가 성공적으로 가져와졌습니다", "com_ui_include_shadcnui": "shadcn/ui 컴포넌트 설치 안내", "com_ui_input": "입력", "com_ui_instructions": "설명", "com_ui_key": "키", "com_ui_late_night": "늦은 밤에도 행복하세요", "com_ui_latest_footer": "모두를 위한 AI, 한곳에서", "com_ui_latest_production_version": "최신 프로덕션 버전", "com_ui_latest_version": "최신 버전", "com_ui_librechat_code_api_key": "LibreChat 코드 인터프리터 API 키 받기", "com_ui_librechat_code_api_subtitle": "안전한 보안. 다국어 지원. 파일 입출력.", "com_ui_librechat_code_api_title": "AI 코드 실행", "com_ui_loading": "로딩 중...", "com_ui_locked": "잠김", "com_ui_logo": "{{0}} 로고", "com_ui_manage": "관리", "com_ui_max_tags": "최대 {{0}}개까지만 허용됩니다. 최신 값을 사용 중입니다.", "com_ui_mcp_servers": "MCP 서버", "com_ui_mcp_url": "MCP 서버 URL", "com_ui_memories": "메모리", "com_ui_memories_allow_create": "메모리 생성 허용", "com_ui_memories_allow_opt_out": "사용자가 메모리 기능을 비활성화할 수 있도록 허용", "com_ui_memories_allow_read": "메모리 읽기 허용", "com_ui_memories_allow_update": "메모리 업데이트 허용", "com_ui_memories_allow_use": "메모리 사용 허용", "com_ui_memories_filter": "메모리 필터링...", "com_ui_memory": "메모리", "com_ui_memory_created": "메모리 생성 완료", "com_ui_memory_deleted": "메모리 삭제 완료", "com_ui_memory_deleted_items": "삭제된 메모리", "com_ui_memory_key_exists": "이 키를 가진 메모리가 이미 존재합니다. 다른 키를 사용해주세요.", "com_ui_memory_updated": "저장된 메모리 업데이트 완료", "com_ui_memory_updated_items": "저장된 메모리", "com_ui_mention": "엔드포인트, 어시스턴트 또는 프리셋을 언급하여 빠르게 전환하세요", "com_ui_min_tags": "최소 {{0}}개는 필수로 입력해야 합니다. 더 이상 값을 제거할 수 없습니다.", "com_ui_misc": "기타", "com_ui_model": "모델", "com_ui_model_parameters": "모델 매개변수", "com_ui_more_info": "자세히 보기", "com_ui_my_prompts": "내 프롬프트", "com_ui_name": "이름", "com_ui_new": "새로 만들기", "com_ui_new_chat": "새 채팅", "com_ui_new_conversation_title": "새 대화 제목", "com_ui_next": "다음", "com_ui_no": "아니요", "com_ui_no_backup_codes": "백업 코드가 없습니다. 새로 생성하세요.", "com_ui_no_bookmarks": "아직 북마크가 없는 것 같습니다. 채팅을 선택하고 새 북마크를 추가해보세요", "com_ui_no_category": "카테고리 없음", "com_ui_no_changes": "업데이트할 변경 사항이 없습니다", "com_ui_no_data": "내용이 비어 있었습니다.", "com_ui_no_personalization_available": "현재 개인화 설정을 사용할 수 없습니다", "com_ui_no_read_access": "메모리를 확인할 수 있는 권한이 없습니다", "com_ui_no_terms_content": "이용 약관 내용이 없습니다", "com_ui_no_valid_items": "내용이 비어 있었습니다.", "com_ui_none": "없음", "com_ui_not_used": "미사용", "com_ui_nothing_found": "찾을 수 없습니다", "com_ui_oauth": "OAuth", "com_ui_of": "/", "com_ui_off": "꺼짐", "com_ui_on": "켜기", "com_ui_openai": "OpenAI", "com_ui_optional": "(선택사항)", "com_ui_page": "페이지", "com_ui_preferences_updated": "설정 업데이트 완료", "com_ui_prev": "이전", "com_ui_preview": "미리보기", "com_ui_privacy_policy": "개인정보 보호정책", "com_ui_privacy_policy_url": "개인정보 보호정책 URL", "com_ui_prompt": "프롬프트", "com_ui_prompt_already_shared_to_all": "이 프롬프트는 이미 모든 사용자와 공유되었습니다", "com_ui_prompt_name": "프롬프트 이름", "com_ui_prompt_name_required": "프롬프트 이름을 입력해주세요", "com_ui_prompt_preview_not_shared": "작성자가 이 프롬프트에 대한 협업을 허용하지 않았습니다.", "com_ui_prompt_text": "프롬프트 텍스트", "com_ui_prompt_text_required": "텍스트를 입력해주세요", "com_ui_prompt_update_error": "프롬프트 업데이트 중 오류가 발생했습니다", "com_ui_prompts": "프롬프트", "com_ui_prompts_allow_create": "프롬프트 생성 허용", "com_ui_prompts_allow_share_global": "모든 사용자와 프롬프트 공유 허용", "com_ui_prompts_allow_use": "프롬프트 사용 허용", "com_ui_provider": "제공자", "com_ui_read_aloud": "소리내어 읽기", "com_ui_redirecting_to_provider": "{{0}}로 이동하는 중입니다. 잠시 기다리세요...", "com_ui_reference_saved_memories": "저장된 메모리 참고", "com_ui_reference_saved_memories_description": "어시스턴트가 답변할 때 저장된 메모리를 참고하고 사용할 수 있도록 허용합니다", "com_ui_refresh_link": "링크 새로고침", "com_ui_regenerate": "재생성", "com_ui_regenerate_backup": "백업 코드 재생성", "com_ui_regenerating": "재생성 중...", "com_ui_region": "지역", "com_ui_rename": "이름 바꾸기", "com_ui_rename_conversation": "대화 이름 변경", "com_ui_rename_failed": "대화 이름 변경 실패", "com_ui_rename_prompt": "프롬프트 이름 변경", "com_ui_requires_auth": "인증이 필요합니다", "com_ui_reset_var": "{{0}} 초기화", "com_ui_result": "결과", "com_ui_revoke": "취소", "com_ui_revoke_info": "사용자가 제공한 자격 증명을 모두 취소합니다.", "com_ui_revoke_key_confirm": "이 키를 취소하시겠습니까?", "com_ui_revoke_key_endpoint": "{{0}} 키 취소", "com_ui_revoke_keys": "키 취소", "com_ui_revoke_keys_confirm": "모든 키를 취소하시겠습니까?", "com_ui_role_select": "역할", "com_ui_roleplay": "역할극", "com_ui_run_code": "코드 실행", "com_ui_run_code_error": "코드 실행 중 오류가 발생했습니다", "com_ui_save": "저장", "com_ui_save_badge_changes": "배지 변경 사항 저장하시겠습니까?", "com_ui_save_submit": "저장 및 제출", "com_ui_saved": "저장되었습니다!", "com_ui_schema": "스키마", "com_ui_scope": "범위", "com_ui_search": "검색", "com_ui_secret_key": "비밀 키", "com_ui_select": "선택", "com_ui_select_all": "모두 선택", "com_ui_select_file": "파일 선택", "com_ui_select_model": "모델 선택", "com_ui_select_provider": "제공자 선택", "com_ui_select_provider_first": "서비스 제공자를 먼저 선택하세요", "com_ui_select_region": "지역 선택", "com_ui_select_search_model": "이름으로 모델 검색", "com_ui_select_search_plugin": "이름으로 플러그인 검색", "com_ui_select_search_provider": "이름으로 공급자 검색", "com_ui_select_search_region": "이름으로 지역 검색", "com_ui_share": "공유하기", "com_ui_share_create_message": "이름과 공유 후에 추가하는 메시지는 비공개로 유지됩니다.", "com_ui_share_delete_error": "공유 링크를 삭제하는 중에 오류가 발생했습니다.", "com_ui_share_error": "채팅 링크를 공유하는 동안 오류가 발생했습니다", "com_ui_share_form_description": "내용이 비어 있었습니다.", "com_ui_share_link_to_chat": "채팅으로 링크 공유하기", "com_ui_share_to_all_users": "모든 사용자와 공유", "com_ui_share_update_message": "이름, 사용자 지정 지침 및 공유 후 추가하는 메시지는 비공개로 유지됩니다.", "com_ui_share_var": "{{0}} 공유하기", "com_ui_shared_link_bulk_delete_success": "공유 링크를 성공적으로 삭제했습니다", "com_ui_shared_link_delete_success": "공유 링크를 성공적으로 삭제했습니다", "com_ui_shared_link_not_found": "공유 링크를 찾을 수 없습니다", "com_ui_shared_prompts": "공유된 프롬프트", "com_ui_shop": "쇼핑", "com_ui_show": "보기", "com_ui_show_all": "전체 보기", "com_ui_show_image_details": "이미지 세부사항 보기", "com_ui_show_qr": "QR 코드 보기", "com_ui_sign_in_to_domain": "{{0}}에 로그인", "com_ui_simple": "간단", "com_ui_size": "크기", "com_ui_special_var_current_date": "현재 날짜", "com_ui_special_var_current_datetime": "현재 날짜와 시간", "com_ui_special_var_current_user": "현재 사용자", "com_ui_special_var_iso_datetime": "UTC ISO 날짜 및 시간", "com_ui_special_variables": "특수 변수:", "com_ui_special_variables_more_info": "드롭다운에서 선택할 수 있는 특수 변수: `{{current_date}}` (오늘의 날짜와 요일), `{{current_datetime}}` (현재 로컬 날짜와 시간), `{{utc_iso_datetime}}` (UTC ISO 날짜 및 시간), `{{current_user}}` (사용자 이름).", "com_ui_speech_while_submitting": "응답 생성 중에는 음성을 전송할 수 없습니다", "com_ui_sr_actions_menu": "\"{{0}}\"의 행동 메뉴 열기", "com_ui_stop": "중지", "com_ui_storage": "저장소", "com_ui_submit": "제출", "com_ui_teach_or_explain": "학습", "com_ui_temporary": "임시 채팅", "com_ui_terms_and_conditions": "이용 약관", "com_ui_terms_of_service": "이용 약관", "com_ui_thinking": "생각중...", "com_ui_thoughts": "생각", "com_ui_token": "토큰", "com_ui_token_exchange_method": "토큰 교환 방식", "com_ui_token_url": "토큰 URL", "com_ui_tokens": "토큰", "com_ui_tools": "도구", "com_ui_travel": "여행", "com_ui_trust_app": "신뢰할 수 있는 어플리케이션", "com_ui_unarchive": "아카이브 해제", "com_ui_unarchive_error": "대화 아카이브 해제 실패", "com_ui_unknown": "알 수 없음", "com_ui_untitled": "제목 없음", "com_ui_update": "업데이트", "com_ui_update_mcp_error": " MCP 생성 혹은 업데이트 중 오류가 발생했습니다.", "com_ui_update_mcp_success": "MCP 생성 혹은 업데이트 완료", "com_ui_upload": "업로드", "com_ui_upload_code_files": "코드 인터프리터용 파일 업로드", "com_ui_upload_delay": "\"{{0}}\" 파일 업로드에 예상보다 시간이 더 걸리고 있습니다. 파일 인덱싱이 완료될 때까지 기다려 주세요.", "com_ui_upload_error": "파일 업로드 중 오류가 발생했습니다", "com_ui_upload_file_context": "파일 컨텍스트 업로드", "com_ui_upload_file_search": "파일 검색용 업로드", "com_ui_upload_files": "파일 업로드", "com_ui_upload_image": "이미지 업로드", "com_ui_upload_image_input": "이미지 업로드", "com_ui_upload_invalid": "업로드할 수 없는 파일입니다. 이미지 파일이어야 하며 용량 제한을 초과하지 않아야 합니다", "com_ui_upload_invalid_var": "업로드할 수 없는 파일입니다. {{0}} MB 이하의 이미지 파일만 가능합니다", "com_ui_upload_ocr_text": "텍스트로 업로드", "com_ui_upload_success": "파일 업로드 성공", "com_ui_upload_type": "업로드 유형 선택", "com_ui_usage": "사용량", "com_ui_use_2fa_code": "2FA 코드 사용", "com_ui_use_backup_code": "백업 코드 사용", "com_ui_use_memory": "메모리 사용", "com_ui_use_micrphone": "마이크 사용", "com_ui_use_prompt": "프롬프트 사용", "com_ui_used": "사용됨", "com_ui_value": "값", "com_ui_variables": "변수", "com_ui_variables_info": "텍스트에 이중 중괄호를 사용하여 변수를 만들 수 있습니다. 예를 들어 `{{예시 변수}}`와 같이 작성하면 나중에 프롬프트를 사용할 때 해당 부분을 채울 수 있습니다.", "com_ui_verify": "확인", "com_ui_version_var": "버전 {{0}}", "com_ui_versions": "버전", "com_ui_view_memory": "메모리 보기", "com_ui_view_source": "원본 채팅 보기", "com_ui_web_search": "웹 검색", "com_ui_web_search_cohere_key": "Cohere API 키 입력", "com_ui_web_search_firecrawl_url": "Firecrawl API URL (선택 사항)", "com_ui_web_search_jina_key": "Jina API 키 입력", "com_ui_web_search_processing": "결과 처리 중", "com_ui_web_search_provider": "검색 제공자", "com_ui_web_search_provider_serper": "Serper API", "com_ui_web_search_provider_serper_key": "Serper API 키 발급받기", "com_ui_web_search_reading": "결과 읽기 중", "com_ui_web_search_reranker": "재정렬기", "com_ui_web_search_reranker_cohere": "Cohere", "com_ui_web_search_reranker_cohere_key": "Cohere API 키 발급받기", "com_ui_web_search_reranker_jina": "Jina AI", "com_ui_web_search_reranker_jina_key": "Jina API 키 발급받기", "com_ui_web_search_scraper": "스크래퍼", "com_ui_web_search_scraper_firecrawl": "Firecrawl API", "com_ui_web_search_scraper_firecrawl_key": "Firecrawl API 키 발급받기", "com_ui_web_searching": "웹 검색 진행 중", "com_ui_web_searching_again": "웹 검색 다시 진행", "com_ui_weekend_morning": "행복한 주말 되세요", "com_ui_write": "작성 중", "com_ui_x_selected": "{{0}}개 선택됨", "com_ui_yes": "네", "com_ui_zoom": "확대/축소", "com_user_message": "당신"}