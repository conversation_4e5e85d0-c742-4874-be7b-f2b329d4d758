<p align="center">
  <img src="client/public/assets/kintu-logo.png" height="70">
  <h1 align="center">
    <a href="https://librechat.ai">Ki<PERSON><PERSON></a>
  </h1>
</p>


# ✨ Funcionalidades

- 🖥️ **UI & Experiência** inspiradas no ChatGPT com design e recursos aprimorados

- 🤖 **Seleção de Modelos IA**:  
  - <PERSON><PERSON><PERSON> (Claude), AWS Bedrock, OpenAI, Azure OpenAI, Google, Vertex AI, OpenAI Assistants API (incl. Azure)
  - [Endpoints personalizados](https://www.librechat.ai/docs/quick_start/custom_endpoints): Use qualquer API compatível com OpenAI no LibreChat, sem necessidade de proxy
  - Compatível com [Provedores de IA Locais & Remotos](https://www.librechat.ai/docs/configuration/librechat_yaml/ai_endpoints):
    - Ollama, groq, Cohere, Mistral AI, Apple MLX, koboldcpp, together.ai,
    - <PERSON>Rout<PERSON>, Perplexity, ShuttleAI, Deepseek, Qwen, e mais

- 🔧 **[API de Interpretador de Código](https://www.librechat.ai/docs/features/code_interpreter)**: 
  - Execução Segura e Isolada em Python, Node.js (JS/TS), Go, C/C++, Java, PHP, Rust e Fortran
  - Manuseio de Arquivos integrado: Envie, processe e faça download diretamente
  - Sem preocupações com privacidade: Execução completamente isolada e segura

- 🔦 **Integração com Agentes & Ferramentas**:  
  - **[Agentes LibreChat](https://www.librechat.ai/docs/features/agents)**:
    - Assistentes Personalizados sem Código: Crie ajudantes especializados baseados em IA sem programação
    - Flexível & Extensível: Anexe ferramentas como DALL-E-3, busca de arquivos, execução de código e muito mais 
    - Compatível com Endpoints Personalizados, OpenAI, Azure, Anthropic, AWS Bedrock, e outros
    - Suporte ao [Protocolo de Contexto de Modelo (MCP)](https://modelcontextprotocol.io/clients#librechat) para ferramentas
  - Use Agentes LibreChat e Assistentes OpenAI com Arquivos, Interpretador de Código, Ferramentas e Ações API

- 🔍 **Busca na Web**:  
  - Pesquise na internet e recupere informações relevantes para enriquecer o contexto da IA
  - Combina provedores de busca, raspadores de conteúdo e reclassificadores de resultados para resultados ótimos
  - **[Saiba mais →](https://www.librechat.ai/docs/features/web_search)**

- 🪄 **Interface Generativa com Artefatos de Código**:  
  - [Artefatos de Código](https://youtu.be/GfTj7O4gmd0?si=WJbdnemZpJzBrJo3) permitem criação de React, HTML e diagramas Mermaid diretamente no chat

- 🎨 **Geração & Edição de Imagens**
  - Texto para imagem e imagem para imagem com [GPT-Image-1](https://www.librechat.ai/docs/features/image_gen#1--openai-image-tools-recommended)
  - Texto para imagem com [DALL-E (3/2)](https://www.librechat.ai/docs/features/image_gen#2--dalle-legacy), [Stable Diffusion](https://www.librechat.ai/docs/features/image_gen#3--stable-diffusion-local), [Flux](https://www.librechat.ai/docs/features/image_gen#4--flux), ou qualquer [MCP server](https://www.librechat.ai/docs/features/image_gen#5--model-context-protocol-mcp)
  - Produza visuais impressionantes a partir de prompts ou refine imagens existentes com uma única instrução

- 💾 **Predefinições & Gerenciamento de Contexto**:  
  - Crie, Salve e Compartilhe Predefinições Personalizadas
  - Alterne entre Endpoints de IA e Predefinições durante o chat
  - Edite, Reenvie e Continue Mensagens com ramificação de conversas
  - [Clone Mensagens & Conversas](https://www.librechat.ai/docs/features/fork) para controle avançado de contexto

- 💬 **Interações Multimodais & com Arquivos**:  
  - Envie e analise imagens com Claude 3, GPT-4.5, GPT-4o, o1, Llama-Vision e Gemini 📸  
  - Converse com Arquivos usando Endpoints Personalizados, OpenAI, Azure, Anthropic, AWS Bedrock e Google 🗃️

- 🌎 **Interface Multilíngue**:  
  - Inglês, 中文, Deutsch, Español, Français, Italiano, Polski, Português Brasileiro
  - Русский, 日本語, Svenska, 한국어, Tiếng Việt, 繁體中文, العربية, Türkçe, Nederlands, עברית

- 🧠 **Interface de Raciocínio**:  
  - Interface Dinâmica para modelos de IA que usam Cadeia de Pensamento/Raciocínio, como DeepSeek-R1

- 🎨 **Interface Personalizável**:  
  - Menu Dropdown & interface personalizáveis que se adaptam a usuários avançados e iniciantes

- 🗣️ **Fala & Áudio**:  
  - Converse sem usar as mãos com Reconhecimento de Voz e Síntese de Voz
  - Envie e reproduza áudios automaticamente
  - Suporte para OpenAI, Azure OpenAI e Elevenlabs

- 📥 **Importação & Exportação de Conversas**:  
  - Importe conversas do LibreChat, ChatGPT, Chatbot UI
  - Exporte conversas como capturas de tela, markdown, texto ou json

- 🔍 **Busca & Descoberta**:  
  - Pesquise em todas as mensagens/conversas

- 👥 **Multiusuário & Acesso Seguro**:
  - Multiusuário com autenticação segura via OAuth2, LDAP e login por email
  - Moderação integrada e ferramentas de controle de uso de tokens

- ⚙️ **Configuração & Implantação**:  
  - Configure proxy, reverse proxy, Docker e muitas opções de implantação 
  - Use totalmente local ou implemente na nuvem

- 📖 **Open-Source & Comunidade**:  
  - Totalmente Open-Source & desenvolvido publicamente
  - Desenvolvimento, suporte e feedback orientados pela comunidade

[Para uma revisão completa das funcionalidades, veja os docs aqui](https://docs.librechat.ai/) 📚

## 🌐 Recursos

**GitHub Repo:**
  - **RAG API:** [github.com/danny-avila/rag_api](https://github.com/danny-avila/rag_api)
  - **Website:** [github.com/LibreChat-AI/librechat.ai](https://github.com/LibreChat-AI/librechat.ai)

**Outros:**
  - **Website:** [librechat.ai](https://librechat.ai)
  - **Documentação:** [librechat.ai/docs](https://librechat.ai/docs)
  - **Blog:** [librechat.ai/blog](https://librechat.ai/blog)
