{"name": "@librechat/api", "version": "1.2.6", "type": "commonjs", "description": "MCP services for LibreChat", "main": "dist/index.js", "module": "dist/index.es.js", "types": "./dist/types/index.d.ts", "exports": {".": {"require": "./dist/index.js", "types": "./dist/types/index.d.ts"}}, "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "npm run clean && rollup -c --bundleConfigAsCjs", "build:watch": "rollup -c -w --bundleConfigAsCjs", "test": "jest --coverage --watch", "test:ci": "jest --coverage --ci", "verify": "npm run test:ci", "b:clean": "bun run rimraf dist", "b:build": "bun run b:clean && bun run rollup -c --silent --bundleConfigAsCjs", "start:everything-sse": "node -r dotenv/config --loader ./tsconfig-paths-bootstrap.mjs --experimental-specifier-resolution=node ./src/examples/everything/sse.ts", "start:everything": "node -r dotenv/config --loader ./tsconfig-paths-bootstrap.mjs --experimental-specifier-resolution=node ./src/demo/everything.ts", "start:filesystem": "node -r dotenv/config --loader ./tsconfig-paths-bootstrap.mjs --experimental-specifier-resolution=node ./src/demo/filesystem.ts", "start:servers": "node -r dotenv/config --loader ./tsconfig-paths-bootstrap.mjs --experimental-specifier-resolution=node ./src/demo/servers.ts"}, "repository": {"type": "git", "url": "git+https://github.com/danny-a<PERSON>/LibreChat.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/danny-a<PERSON>/LibreChat/issues"}, "homepage": "https://librechat.ai", "devDependencies": {"@babel/preset-env": "^7.21.5", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.21.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-commonjs": "^25.0.2", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.1.0", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/bun": "^1.2.15", "@types/diff": "^6.0.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.2", "@types/multer": "^1.4.13", "@types/node": "^20.3.0", "@types/react": "^18.2.18", "@types/winston": "^2.4.4", "jest": "^29.5.0", "jest-junit": "^16.0.0", "librechat-data-provider": "*", "rimraf": "^5.0.1", "rollup": "^4.22.4", "rollup-plugin-generate-package-json": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.4", "ts-node": "^10.9.2", "typescript": "^5.0.4"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "peerDependencies": {"@librechat/agents": "^2.4.59", "@librechat/data-schemas": "*", "@modelcontextprotocol/sdk": "^1.13.3", "axios": "^1.8.2", "diff": "^7.0.0", "eventsource": "^3.0.2", "express": "^4.21.2", "js-yaml": "^4.1.0", "keyv": "^5.3.2", "librechat-data-provider": "*", "node-fetch": "2.7.0", "tiktoken": "^1.0.15", "undici": "^7.10.0", "zod": "^3.22.4"}}