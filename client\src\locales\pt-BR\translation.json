{"chat_direction_left_to_right": "algo precisa ir aqui. Está vazio", "chat_direction_right_to_left": "algo precisa ir aqui. esta vazio", "com_a11y_ai_composing": "A IA ainda está compondo.", "com_a11y_end": "A IA terminou de responder.", "com_a11y_start": "A IA começou a responder.", "com_agents_allow_editing": "Permitir que outros utilizadores editem o seu agente", "com_agents_by_librechat": "por <PERSON>ú", "com_agents_code_interpreter": "<PERSON>uando ativado, permite que seu agente aproveite a API do interpretador de código Kintú para executar o código gerado, incluindo o processamento de arquivos, com segurança. Requer uma chave de API válida.", "com_agents_code_interpreter_title": "API do Interpretador de Código", "com_agents_create_error": "Houve um erro ao criar seu agente.", "com_agents_description_placeholder": "Opcional: Descreva seu Agente aqui", "com_agents_enable_file_search": "<PERSON><PERSON><PERSON>", "com_agents_file_context": "Contexto de arquivo (OCR)", "com_agents_file_context_disabled": "O agente deve ser criado antes de carregar arquivos para o Contexto de Arquivo.", "com_agents_file_context_info": "Os arquivos carregados como \"Contexto\" são processados ​​usando OCR para extrair texto, que é então adicionado às instruções do Agente. Ideal para documentos, imagens com texto ou PDFs onde você precisa do conteúdo de texto completo de um arquivo", "com_agents_file_search_disabled": "O agente deve ser criado antes de carregar arquivos para Pesquisa de Arquivos.", "com_agents_file_search_info": "<PERSON>uando at<PERSON>, o agente será informado dos nomes exatos dos arquivos listados abaixo, permitindo que ele recupere o contexto relevante desses arquivos.", "com_agents_instructions_placeholder": "As instruções do sistema que o agente usa", "com_agents_mcp_description_placeholder": "Explique o que ele faz em poucas palavras", "com_agents_mcp_icon_size": "<PERSON><PERSON><PERSON> 128 x 128 px", "com_agents_mcp_info": "Adicione servidores MCP ao seu agente para permitir que ele execute tarefas e interaja com serviços externos", "com_agents_mcp_name_placeholder": "Ferramenta personalizada", "com_agents_mcp_trust_subtext": "Conectores personalizados não são verificados pelo Kintú", "com_agents_mcps_disabled": "Você precisa criar um agente antes de adicionar MCPs.", "com_agents_missing_provider_model": "Selecione um provedor e um modelo antes de criar um agente.\n", "com_agents_name_placeholder": "Opcional: O nome do agente", "com_agents_no_access": "Não tens permissões para editar este agente.", "com_agents_no_agent_id_error": "Nenhum ID de agente encontrado. Certifique-se de que o agente seja criado primeiro.", "com_agents_not_available": "Agente não disponível.", "com_agents_search_name": "Pesquisar agentes por nome", "com_agents_update_error": "Houve um erro ao atualizar seu agente.", "com_assistants_action_attempt": "Assistente quer falar com {{0}}", "com_assistants_actions": "Ações", "com_assistants_actions_disabled": "Você precisa criar um assistente antes de adicionar ações.", "com_assistants_actions_info": "Permita que seu Assistente recupere informações ou execute ações via API's", "com_assistants_add_actions": "Adicionar <PERSON>", "com_assistants_add_tools": "<PERSON><PERSON><PERSON><PERSON>", "com_assistants_allow_sites_you_trust": "Permitir apenas sites em que confia.", "com_assistants_append_date": "Anexar Data e Hora Atual", "com_assistants_append_date_tooltip": "Quando ativado, a data e hora atual do cliente serão anexadas às instruções do sistema do assistente.", "com_assistants_attempt_info": "O Assistente deseja enviar o seguinte:", "com_assistants_available_actions": "Ações Disponíveis", "com_assistants_capabilities": "Capacidades", "com_assistants_code_interpreter": "Interpretador de Código", "com_assistants_code_interpreter_files": "Os arquivos abaixo são apenas para o Interpretador de Código:", "com_assistants_code_interpreter_info": "O Interpretador de Código permite que o assistente escreva e execute código. Esta ferramenta pode processar arquivos com dados e formatações diversas, e gerar arquivos como gráficos.", "com_assistants_completed_action": "<PERSON><PERSON><PERSON> com {{0}}", "com_assistants_completed_function": "Executou {{0}}", "com_assistants_conversation_starters": "Iniciadores de Conversa", "com_assistants_conversation_starters_placeholder": "Digite um iniciador de conversa", "com_assistants_create_error": "Houve um erro ao criar seu assistente.", "com_assistants_create_success": "Criado com sucesso", "com_assistants_delete_actions_error": "Houve um erro ao excluir a ação.", "com_assistants_delete_actions_success": "Ação excluída com sucesso do Assistente", "com_assistants_description_placeholder": "Opcional: Descreva seu Assistente aqui", "com_assistants_domain_info": "Assistente enviou esta informação para {{0}}", "com_assistants_file_search": "Pesquisa de Arquivos", "com_assistants_file_search_info": "A pesquisa de arquivos permite que o assistente tenha conhecimento dos arquivos que você ou seus usuários carregam. Uma vez que um arquivo é carregado, o assistente decide automaticamente quando recuperar o conteúdo com base nas solicitações do usuário. Anexar armazenamentos vetoriais para Pesquisa de Arquivos ainda não é suportado. Você pode anexá-los no Playground do Provedor ou anexar arquivos às mensagens para pesquisa de arquivos em uma base de thread.", "com_assistants_function_use": "Assistente usou {{0}}", "com_assistants_image_vision": "Visão de Imagem", "com_assistants_instructions_placeholder": "As instruções do sistema que o assistente usa", "com_assistants_knowledge": "Conhecimento", "com_assistants_knowledge_disabled": "O assistente deve ser criado, e o Interpretador de Código ou Recuperação deve ser habilitado e salvo antes de carregar arquivos como Conhecimento.", "com_assistants_knowledge_info": "Se você carregar arquivos em Conhecimento, as conversas com seu Assistente podem incluir o conteúdo dos arquivos.", "com_assistants_max_starters_reached": "Número máximo de iniciadores de conversa atingido", "com_assistants_name_placeholder": "Opcional: O nome do assistente", "com_assistants_non_retrieval_model": "A pesquisa de arquivos não está habilitada neste modelo. Por favor, selecione outro modelo.", "com_assistants_retrieval": "Recuperação", "com_assistants_running_action": "Executando ação", "com_assistants_running_var": "<PERSON> andamento {{0}}", "com_assistants_search_name": "Pesquisar assistentes por nome", "com_assistants_update_actions_error": "Houve um erro ao criar ou atualizar a ação.", "com_assistants_update_actions_success": "Ação criada ou atualizada com sucesso", "com_assistants_update_error": "Houve um erro ao atualizar seu assistente.", "com_assistants_update_success": "Atualizado com sucesso", "com_auth_already_have_account": "Já tem uma conta?", "com_auth_apple_login": "Iniciar sessão com a Apple", "com_auth_back_to_login": "Voltar para Login", "com_auth_click": "Clique", "com_auth_click_here": "Clique aqui", "com_auth_continue": "<PERSON><PERSON><PERSON><PERSON>", "com_auth_create_account": "Criar sua conta", "com_auth_discord_login": "Continuar com Discord", "com_auth_email": "E-mail", "com_auth_email_address": "Endereço de e-mail", "com_auth_email_max_length": "O e-mail não deve ter mais de 120 caracteres", "com_auth_email_min_length": "O e-mail deve ter pelo menos 6 caracteres", "com_auth_email_pattern": "Você deve inserir um endereço de e-mail válido", "com_auth_email_required": "E-mail é obrigatório", "com_auth_email_resend_link": "Reenviar E-mail", "com_auth_email_resent_failed": "Falha ao reenviar e-mail de verificação", "com_auth_email_resent_success": "E-mail de verificação reenviado com sucesso", "com_auth_email_verification_failed": "Falha na verificação de e-mail", "com_auth_email_verification_failed_token_missing": "Falha na verificação, token ausente", "com_auth_email_verification_in_progress": "Verificando seu e-mail, por favor, aguarde", "com_auth_email_verification_invalid": "Verificação de e-mail inválida", "com_auth_email_verification_redirecting": "Redirecionando em {{0}} segundos...", "com_auth_email_verification_resend_prompt": "Não recebeu o e-mail?", "com_auth_email_verification_success": "E-mail verificado com sucesso", "com_auth_email_verifying_ellipsis": "Verificando...", "com_auth_error_create": "Houve um erro ao tentar registrar sua conta. Por favor, tente novamente.", "com_auth_error_invalid_reset_token": "Este token de redefinição de senha não é mais válido.", "com_auth_error_login": "Não foi possível fazer login com as informações fornecidas. Por favor, verifique suas credenciais e tente novamente.", "com_auth_error_login_ban": "Sua conta foi temporariamente banida devido a violações do nosso serviço.", "com_auth_error_login_rl": "Muitas tentativas de login em um curto período de tempo. Por favor, tente novamente mais tarde.", "com_auth_error_login_server": "Houve um erro interno no servidor. Por favor, aguarde alguns momentos e tente novamente.", "com_auth_error_login_unverified": "Sua conta não foi verificada. Por favor, verifique seu e-mail para um link de verificação.", "com_auth_facebook_login": "Continuar com Facebook", "com_auth_full_name": "Nome completo", "com_auth_github_login": "Continuar com Github", "com_auth_google_login": "Continuar com Google", "com_auth_here": "AQUI", "com_auth_login": "Entrar", "com_auth_login_with_new_password": "Agora você pode fazer login com sua nova senha.", "com_auth_name_max_length": "O nome deve ter menos de 80 caracteres", "com_auth_name_min_length": "O nome deve ter pelo menos 3 caracteres", "com_auth_name_required": "Nome é obrigatório", "com_auth_no_account": "Não tem uma conta?", "com_auth_password": "<PERSON><PERSON>", "com_auth_password_confirm": "Confirmar <PERSON><PERSON><PERSON>", "com_auth_password_forgot": "Esque<PERSON>u a senha?", "com_auth_password_max_length": "A senha deve ter menos de 128 caracteres", "com_auth_password_min_length": "A senha deve ter pelo menos 8 caracteres", "com_auth_password_not_match": "As senhas não coincidem", "com_auth_password_required": "Senha é obrigatória", "com_auth_registration_success_generic": "Por favor, verifique seu e-mail para verificar seu endereço de e-mail.", "com_auth_registration_success_insecure": "Registro bem-sucedido.", "com_auth_reset_password": "Redefinir sua senha", "com_auth_reset_password_if_email_exists": "Se uma conta com esse e-mail existir, um e-mail com instruções para redefinir a senha foi enviado. Certifique-se de verificar sua pasta de spam.", "com_auth_reset_password_link_sent": "E-mail enviado", "com_auth_reset_password_success": "Senha redefinida com sucesso", "com_auth_sign_in": "Entrar", "com_auth_sign_up": "Inscrever-se", "com_auth_submit_registration": "Enviar registro", "com_auth_to_reset_your_password": "para redefinir sua senha.", "com_auth_to_try_again": "para tentar novamente.", "com_auth_two_factor": "Consulte a sua aplicação de senha de uso único preferida para obter um código", "com_auth_username": "Nome de usuário (opcional)", "com_auth_username_max_length": "O nome de usuário deve ter menos de 20 caracteres", "com_auth_username_min_length": "O nome de usuário deve ter pelo menos 2 caracteres", "com_auth_verify_your_identity": "Verificar a sua identidade", "com_auth_welcome_back": "<PERSON><PERSON> vindo ao <PERSON>", "com_citation_more_details": "<PERSON><PERSON> detalhes sobre {{label}}", "com_citation_source": "Fonte", "com_click_to_download": "(clique aqui para download)", "com_download_expired": "(download expirado)", "com_download_expires": "(clique aqui para download - expira {{0}})", "com_endpoint": "Endpoint", "com_endpoint_agent": "<PERSON><PERSON>", "com_endpoint_agent_model": "<PERSON><PERSON>e (Recomendado: GPT-3.5)", "com_endpoint_agent_placeholder": "Selecione um agente", "com_endpoint_ai": "AI", "com_endpoint_anthropic_maxoutputtokens": "Número máximo de tokens que podem ser gerados na resposta. Especifique um valor mais baixo para respostas mais curtas e um valor mais alto para respostas mais longas. Nota: os modelos podem parar antes de atingir esse máximo.", "com_endpoint_anthropic_prompt_cache": "O cache de prompt permite reutilizar um grande contexto ou instruções em chamadas de API, reduzindo custos e latência", "com_endpoint_anthropic_temp": "Varia de 0 a 1. Use temperatura mais próxima de 0 para tarefas analíticas / de múltipla escolha, e mais próxima de 1 para tarefas criativas e generativas. Recomendamos alterar isso ou Top P, mas não ambos.", "com_endpoint_anthropic_thinking": "Permite o raciocínio interno para os modelos Claude <PERSON> (3.7 Sonnet). Nota: requer que o \"Orçamento de raciocínio\" esteja definido e seja inferior ao \"Máximo de tokens de saída\"", "com_endpoint_anthropic_thinking_budget": "Determina o número máximo de tokens que o Claude pode utilizar para o seu processo de raciocínio interno. Orçamentos maiores podem melhorar a qualidade da resposta, permitindo uma análise mais completa para problemas complexos, embora o <PERSON> possa não usar todo o orçamento alocado, especialmente em intervalos acima de 32K. Essa configuração deve ser menor que \"Máximo de tokens de saída\".", "com_endpoint_anthropic_topk": "Top-k altera como o modelo seleciona tokens para saída. Um top-k de 1 significa que o token selecionado é o mais provável entre todos os tokens no vocabulário do modelo (também chamado de decodificação gananciosa), enquanto um top-k de 3 significa que o próximo token é selecionado entre os 3 tokens mais prováveis (usando temperatura).", "com_endpoint_anthropic_topp": "Top-p altera como o modelo seleciona tokens para saída. Os tokens são selecionados dos mais prováveis (veja o parâmetro topK) até os menos prováveis até que a soma de suas probabilidades atinja o valor top-p.", "com_endpoint_assistant": "<PERSON><PERSON><PERSON>", "com_endpoint_assistant_model": "<PERSON><PERSON> Assistente", "com_endpoint_assistant_placeholder": "Por favor, selecione um Assistente no Painel Lateral Direito", "com_endpoint_completion": "<PERSON><PERSON><PERSON><PERSON>", "com_endpoint_completion_model": "<PERSON><PERSON> de Conclusão (Recomendado: GPT-4)", "com_endpoint_config_click_here": "Clique Aqui", "com_endpoint_config_google_api_info": "Para obter sua chave API de Linguagem Generativa (para Gemini),", "com_endpoint_config_google_api_key": "Chave API do Google", "com_endpoint_config_google_cloud_platform": "(do Google Cloud Platform)", "com_endpoint_config_google_gemini_api": "(API Gemini)", "com_endpoint_config_google_service_key": "Chave de Conta de Serviço do Google", "com_endpoint_config_key": "Definir Chave API", "com_endpoint_config_key_encryption": "Sua chave será criptografada e excluída em", "com_endpoint_config_key_for": "Definir Chave API para", "com_endpoint_config_key_google_need_to": "Você precisa", "com_endpoint_config_key_google_service_account": "Criar uma Conta de Serviço", "com_endpoint_config_key_google_vertex_ai": "Habilitar Vertex AI", "com_endpoint_config_key_google_vertex_api": "API no Google Cloud, então", "com_endpoint_config_key_google_vertex_api_role": "Certifique-se de clicar em \"Criar e Continuar\" para dar pelo menos o papel de \"Usuário do Vertex AI\". Por fim, crie uma chave JSON para importar aqui.", "com_endpoint_config_key_import_json_key": "Importar Chave JSON da Conta de Serviço.", "com_endpoint_config_key_import_json_key_invalid": "Chave JSON da Conta de Serviço Inválida, Você importou o arquivo correto?", "com_endpoint_config_key_import_json_key_success": "Chave JSON da Conta de Serviço Importada com Sucesso", "com_endpoint_config_key_name": "Chave", "com_endpoint_config_key_never_expires": "Sua chave nunca expira", "com_endpoint_config_placeholder": "Defina sua Chave no menu do Cabeçalho para conversar.", "com_endpoint_config_value": "Insira o valor para", "com_endpoint_context": "Contexto", "com_endpoint_context_info": "O número máximo de tokens que podem ser usados para contexto. Use isso para controlar quantos tokens são enviados por solicitação. Se não especificado, usará os padrões do sistema com base no tamanho do contexto dos modelos conhecidos. Definir valores mais altos pode resultar em erros e/ou maior custo de tokens.", "com_endpoint_context_tokens": "Máximo de Tokens de Contexto", "com_endpoint_custom_name": "Nome Personalizado", "com_endpoint_default": "<PERSON><PERSON><PERSON>", "com_endpoint_default_blank": "padrão: em branco", "com_endpoint_default_empty": "padrão: vazio", "com_endpoint_default_with_num": "padrão: {{0}}", "com_endpoint_deprecated": "Obsoleto", "com_endpoint_deprecated_info": "Este ponto de extremidade está obsoleto e pode ser removido em versões futuras. Em vez disso, use o ponto de extremidade do agente.", "com_endpoint_deprecated_info_a11y": "O ponto de extremidade do plugin está obsoleto e pode ser removido em versões futuras. Em vez disso, use o ponto de extremidade do agente.", "com_endpoint_examples": "Presets", "com_endpoint_export": "Exportar", "com_endpoint_export_share": "Exportar/Compartilhar", "com_endpoint_frequency_penalty": "Penalidade de Frequência", "com_endpoint_func_hover": "Habilitar uso de Plugins como Funções OpenAI", "com_endpoint_google_custom_name_placeholder": "Defina um nome personalizado para o Google", "com_endpoint_google_maxoutputtokens": "Número máximo de tokens que podem ser gerados na resposta. Especifique um valor mais baixo para respostas mais curtas e um valor mais alto para respostas mais longas. Nota: os modelos podem parar antes de atingir esse máximo.", "com_endpoint_google_temp": "Valores mais altos = mais ale<PERSON><PERSON><PERSON>, enquanto valores mais baixos = mais focado e determinístico. Recomendamos alterar isso ou Top P, mas não ambos.", "com_endpoint_google_topk": "Top-k altera como o modelo seleciona tokens para saída. Um top-k de 1 significa que o token selecionado é o mais provável entre todos os tokens no vocabulário do modelo (também chamado de decodificação gananciosa), enquanto um top-k de 3 significa que o próximo token é selecionado entre os 3 tokens mais prováveis (usando temperatura).", "com_endpoint_google_topp": "Top-p altera como o modelo seleciona tokens para saída. Os tokens são selecionados dos mais prováveis (veja o parâmetro topK) até os menos prováveis até que a soma de suas probabilidades atinja o valor top-p.", "com_endpoint_instructions_assistants": "Substituir Instruções", "com_endpoint_instructions_assistants_placeholder": "Substitui as instruções do assistente. Isso é útil para modificar o comportamento em uma base por execução.", "com_endpoint_max_output_tokens": "Máximo de Tokens de Saída", "com_endpoint_message": "Mensagem", "com_endpoint_message_new": "Mensagem {{0}}", "com_endpoint_message_not_appendable": "Edite sua mensagem ou Regenerar.", "com_endpoint_my_preset": "<PERSON><PERSON>", "com_endpoint_no_presets": "Ainda não há presets, use o botão de configurações para criar um", "com_endpoint_open_menu": "<PERSON><PERSON><PERSON>", "com_endpoint_openai_custom_name_placeholder": "Defina um nome personalizado para a IA", "com_endpoint_openai_detail": "A resolução para solicitações de Visão. \"Baixa\" é mais barata e rápida, \"Alta\" é mais detalhada e cara, e \"Auto\" escolherá automaticamente entre as duas com base na resolução da imagem.", "com_endpoint_openai_freq": "Número entre -2.0 e 2.0. Valores positivos penalizam novos tokens com base em sua frequência existente no texto até agora, diminuindo a probabilidade do modelo de repetir a mesma linha literalmente.", "com_endpoint_openai_max": "O máximo de tokens para gerar. O comprimento total dos tokens de entrada e dos tokens gerados é limitado pelo comprimento do contexto do modelo.", "com_endpoint_openai_max_tokens": "Campo opcional `max_tokens`, representando o número máximo de tokens que podem ser gerados na conclusão do chat. O comprimento total dos tokens de entrada e dos tokens gerados é limitado pelo comprimento do contexto dos modelos. Você pode experimentar erros se esse número exceder o máximo de tokens de contexto.", "com_endpoint_openai_pres": "Número entre -2.0 e 2.0. Valores positivos penalizam novos tokens com base em sua presença no texto até agora, aumentando a probabilidade do modelo de falar sobre novos tópicos.", "com_endpoint_openai_prompt_prefix_placeholder": "Defina instruções personalizadas para incluir na Mensagem do Sistema. Padrão: nenhuma", "com_endpoint_openai_reasoning_effort": "somente modelos o1 e o3: restringe o esforço de raciocínio para modelos de raciocínio. Reduzir o esforço de raciocínio pode resultar em respostas mais rápidas e menos tokens usados ​​no raciocínio em uma resposta.", "com_endpoint_openai_resend": "<PERSON><PERSON><PERSON>r todas as imagens anexadas anteriormente. Nota: isso pode aumentar significativamente o custo de tokens e você pode experimentar erros com muitos anexos de imagem.", "com_endpoint_openai_resend_files": "Reenviar todos os arquivos anexados anteriormente. Nota: isso aumentará o custo de tokens e você pode experimentar erros com muitos anexos.", "com_endpoint_openai_stop": "Até 4 sequências onde a API parará de gerar mais tokens.", "com_endpoint_openai_temp": "Valores mais altos = mais ale<PERSON><PERSON><PERSON>, enquanto valores mais baixos = mais focado e determinístico. Recomendamos alterar isso ou Top P, mas não ambos.", "com_endpoint_openai_topp": "Uma alternativa à amostragem com temperatura, chamada amostragem de núcleo, onde o modelo considera os resultados dos tokens com massa de probabilidade top_p. Então, 0.1 significa que apenas os tokens que compreendem os 10% principais da massa de probabilidade são considerados. Recomendamos alterar isso ou a temperatura, mas não ambos.", "com_endpoint_output": "<PERSON><PERSON><PERSON>", "com_endpoint_plug_image_detail": "<PERSON><PERSON><PERSON>", "com_endpoint_plug_resend_files": "Reenviar Arquivos", "com_endpoint_plug_set_custom_instructions_for_gpt_placeholder": "Defina instruções personalizadas para incluir na Mensagem do Sistema. Padrão: nenhuma", "com_endpoint_plug_skip_completion": "<PERSON><PERSON> Conclusão", "com_endpoint_plug_use_functions": "Usar Funções", "com_endpoint_presence_penalty": "Penalidade de Presença", "com_endpoint_preset": "preset", "com_endpoint_preset_custom_name_placeholder": "algo precisa ir aqui. esta vazio", "com_endpoint_preset_default": "é agora o preset padrão.", "com_endpoint_preset_default_item": "Padrão:", "com_endpoint_preset_default_none": "Nenhum preset pad<PERSON><PERSON> ativo.", "com_endpoint_preset_default_removed": "não é mais o preset padrão.", "com_endpoint_preset_delete_confirm": "Tem certeza de que deseja excluir este preset?", "com_endpoint_preset_delete_error": "Houve um erro ao excluir seu preset. Por favor, tente novamente.", "com_endpoint_preset_import": "Preset Importado!", "com_endpoint_preset_import_error": "Houve um erro ao importar seu preset. Por favor, tente novamente.", "com_endpoint_preset_name": "Nome do Preset", "com_endpoint_preset_save_error": "Houve um erro ao salvar seu preset. Por favor, tente novamente.", "com_endpoint_preset_selected": "Preset Ativo!", "com_endpoint_preset_selected_title": "Ativo!", "com_endpoint_preset_title": "Preset", "com_endpoint_presets": "presets", "com_endpoint_presets_clear_warning": "Tem certeza de que deseja limpar todos os presets? Is<PERSON> é irreversível.", "com_endpoint_prompt_cache": "<PERSON><PERSON>", "com_endpoint_prompt_prefix": "Instruções Personalizadas", "com_endpoint_prompt_prefix_assistants": "Instruções Adicionais", "com_endpoint_prompt_prefix_assistants_placeholder": "Defina instruções ou contexto adicionais além das instruções principais do Assistente. Ignorado se vazio.", "com_endpoint_prompt_prefix_placeholder": "Defina instruções ou contexto personalizados. Ignorado se vazio.", "com_endpoint_reasoning_effort": "Esforço de raciocínio", "com_endpoint_save_as_preset": "<PERSON><PERSON>", "com_endpoint_search": "Procurar endpoint por nome", "com_endpoint_set_custom_name": "Defina um nome personalizado, caso você possa encontrar este preset", "com_endpoint_skip_hover": "Habilitar pular a etapa de conclusão, que revisa a resposta final e os passos gerados", "com_endpoint_stop": "Sequências de Parada", "com_endpoint_stop_placeholder": "Separe os valores pressionando `Enter`", "com_endpoint_temperature": "Temperatura", "com_endpoint_thinking": "Pensamento", "com_endpoint_thinking_budget": "Pensar no orçamento", "com_endpoint_top_k": "Top K", "com_endpoint_top_p": "Top P", "com_endpoint_use_active_assistant": "Usar Assistente Ativo", "com_error_expired_user_key": "A chave fornecida para {{0}} expirou em {{1}}. Por favor, forneça uma nova chave e tente novamente.", "com_error_files_dupe": "Foi detectado um arquivo duplicado.", "com_error_files_empty": "Pensamento", "com_error_files_process": "Ocorreu um erro ao processar o arquivo.", "com_error_files_unsupported_capability": "Não existem capacidades ativadas que suportem este tipo de arquivo.", "com_error_files_upload": "Ocorreu um erro ao carregar o arquivo.", "com_error_files_upload_canceled": "O pedido de carregamento de arquivos foi cancelado. Nota: o carregamento de arquivo pode ainda estar a ser processado e terá de ser eliminado manualmente.", "com_error_files_validation": "Ocorreu um erro durante a validação do arquivo.", "com_error_input_length": "A contagem de tokens da última mensagem é muito longa, excedendo o limite de tokens, ou seus parâmetros de limite de tokens estão configurados incorretamente, afetando negativamente a janela de contexto. Mais informações: {{0}}. Encurte sua mensagem, ajuste o tamanho máximo do contexto nos parâmetros da conversa ou bifurque a conversa para continuar.", "com_error_invalid_user_key": "Chave fornecida inválida. Por favor, forneça uma chave válida e tente novamente.", "com_error_moderation": "Parece que o conteúdo enviado foi sinalizado pelo nosso sistema de moderação por não estar alinhado com nossas diretrizes da comunidade. Não podemos prosseguir com este tópico específico. Se você tiver outras perguntas ou tópicos que gostaria de explorar, edite sua mensagem ou crie uma nova conversa.", "com_error_no_base_url": "Nenhuma URL base encontrada. Por favor, forneça uma e tente novamente.", "com_error_no_user_key": "Nenhuma chave encontrada. Por favor, forneça uma chave e tente novamente.", "com_files_filter": "Filtrar arquivos...", "com_files_no_results": "<PERSON><PERSON>hum resultado.", "com_files_number_selected": "{{0}} de {{1}} arquivo(s) selecionado(s)", "com_files_table": "algo precisa ir aqui. esta vazio", "com_generated_files": "Arquivos gerados:", "com_hide_examples": "Ocultar Exemplos", "com_nav_2fa": "Autenticação de dois fatores (2FA)", "com_nav_account_settings": "Configurações da Conta", "com_nav_always_make_prod": "Sempre tornar novas versões produção", "com_nav_archive_created_at": "Data de Arquivamento", "com_nav_archive_name": "Nome", "com_nav_archived_chats": "Chats Arquivados", "com_nav_at_command": "Comando @", "com_nav_at_command_description": "Alternar comando \"@\" para alternar endpoints, modelos, predefinições, etc.", "com_nav_audio_play_error": "Erro ao reproduzir á<PERSON>: {{0}}", "com_nav_audio_process_error": "Erro ao processar áudio: {{0}}", "com_nav_auto_scroll": "Rolagem Automática para a última mensagem ao abrir o chat", "com_nav_auto_send_prompts": "Enviar prompts automaticamente", "com_nav_auto_send_text": "Enviar texto automaticamente", "com_nav_auto_send_text_disabled": "definir -1 para desativar", "com_nav_auto_transcribe_audio": "Transcrever áudio automaticamente", "com_nav_automatic_playback": "Reprodução Automática da Última Mensagem", "com_nav_balance": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_browser": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_change_picture": "<PERSON>dar foto", "com_nav_chat_commands": "Comandos do chat", "com_nav_chat_commands_info": "Estes comandos são ativados digitando caracteres específicos no início da sua mensagem. Cada comando é acionado pelo seu prefixo designado. Pode desativá-los se utilizar frequentemente estes caracteres para iniciar mensagens.", "com_nav_chat_direction": "Direção do chat", "com_nav_clear_all_chats": "<PERSON><PERSON> todos os chats", "com_nav_clear_cache_confirm_message": "Tem a certeza de que quer limpar a cache?", "com_nav_clear_conversation": "Limpar conversas", "com_nav_clear_conversation_confirm_message": "Tem certeza de que deseja limpar todas as conversas? Isso é irreversível.", "com_nav_close_sidebar": "<PERSON><PERSON><PERSON> barra lateral", "com_nav_commands": "<PERSON><PERSON><PERSON>", "com_nav_confirm_clear": "Confirmar <PERSON>", "com_nav_conversation_mode": "<PERSON><PERSON> de Conversa", "com_nav_convo_menu_options": "Opções do Menu de Conversa", "com_nav_db_sensitivity": "Sensibilidade de decibéis", "com_nav_delete_account": "Excluir conta", "com_nav_delete_account_button": "Excluir minha conta permanentemente", "com_nav_delete_account_confirm": "Excluir conta - você tem certeza?", "com_nav_delete_account_email_placeholder": "Por favor, insira o e-mail da sua conta", "com_nav_delete_cache_storage": "Excluir armazenamento de cache TTS", "com_nav_delete_data_info": "Todos os seus dados serão excluídos.", "com_nav_delete_warning": "AVISO: Isso excluirá permanentemente sua conta.", "com_nav_enable_cache_tts": "Habilitar cache TTS", "com_nav_enable_cloud_browser_voice": "Usar vozes baseadas na nuvem", "com_nav_enabled": "Habilitado", "com_nav_engine": "Motor", "com_nav_enter_to_send": "Pressione Enter para enviar mensagens", "com_nav_export": "Exportar", "com_nav_export_all_message_branches": "Exportar todos os ramos de mensagens", "com_nav_export_conversation": "Exportar conversa", "com_nav_export_filename": "Nome do arquivo", "com_nav_export_filename_placeholder": "Definir o nome do arquivo", "com_nav_export_include_endpoint_options": "Incluir opções de endpoint", "com_nav_export_recursive": "Recursivo", "com_nav_export_recursive_or_sequential": "Recursivo ou sequencial?", "com_nav_export_type": "Tipo", "com_nav_external": "Externo", "com_nav_font_size": "<PERSON><PERSON><PERSON> da Mensagem", "com_nav_font_size_base": "Médio", "com_nav_font_size_lg": "Grande", "com_nav_font_size_sm": "Pequeno", "com_nav_font_size_xl": "Extra Grande", "com_nav_font_size_xs": "Extra Pequeno", "com_nav_help_faq": "Ajuda & FAQ", "com_nav_hide_panel": "Ocultar painel mais à direita", "com_nav_info_code_artifacts": "Habilita a exibição de artefatos de código experimental ao lado do chat", "com_nav_info_code_artifacts_agent": "Ativa a utilização de artefatos de código para este agente. Por predefinição, são adicionadas instruções adicionais específicas para a utilização de artefatos, a menos que o \"Modo de aviso personalizado\" esteja ativado.", "com_nav_info_custom_prompt_mode": "<PERSON>uando habilitado, o prompt padrão do sistema de artefatos não será incluído. <PERSON><PERSON> as instruções de geração de artefatos devem ser fornecidas manualmente neste modo.", "com_nav_info_enter_to_send": "<PERSON>uan<PERSON> ha<PERSON>, pressionar `ENTER` enviará sua mensagem. <PERSON>uando desabilitado, pressionar Enter adicionará uma nova linha, e você precisará pressionar `CTRL + ENTER` / `⌘ + ENTER` para enviar sua mensagem.", "com_nav_info_fork_change_default": "`Apenas mensagens visíveis` inclui apenas o caminho direto para a mensagem selecionada. `Incluir ramos relacionados` adiciona ramos ao longo do caminho. `Incluir tudo de/para aqui` inclui todas as mensagens e ramos conectados.", "com_nav_info_fork_split_target_setting": "Quando habilitado, a bifurcação começará da mensagem alvo até a última mensagem na conversa, de acordo com o comportamento selecionado.", "com_nav_info_include_shadcnui": "Quando habilitado, as instruções para usar componentes shadcn/ui serão incluídas. shadcn/ui é uma coleção de componentes reutilizáveis construídos usando Radix UI e Tailwind CSS. Nota: estas são instruções longas, você deve habilitar apenas se for importante informar o LLM sobre as importações e componentes corretos. Para mais informações sobre esses componentes, visite: https://ui.shadcn.com/", "com_nav_info_latex_parsing": "Quando habilitado, o código LaTeX nas mensagens será renderizado como equações matemáticas. Desabilitar isso pode melhorar o desempenho se você não precisar de renderização LaTeX.", "com_nav_info_save_draft": "Quando habilitado, o texto e os anexos que você inserir no formulário de chat serão salvos automaticamente localmente como rascunhos. Esses rascunhos estarão disponíveis mesmo se você recarregar a página ou mudar para uma conversa diferente. Os rascunhos são armazenados localmente no seu dispositivo e são excluídos uma vez que a mensagem é enviada.", "com_nav_info_show_thinking": "Quando ativado, o chat apresentará os menus pendentes de raciocínio abertos por predefinição, permitindo-lhe ver o raciocínio da IA em tempo real. Quando desativado, os menus suspensos de raciocínio permanecerão fechados por predefinição para uma interface mais limpa e simplificada", "com_nav_info_user_name_display": "<PERSON>uando habilitado, o nome de usuário do remetente será mostrado acima de cada mensagem que você enviar. Quando desabilitado, você verá apenas \"Você\" acima de suas mensagens.", "com_nav_lang_arabic": "العربية", "com_nav_lang_auto": "Detecção automática", "com_nav_lang_brazilian_portuguese": "Portuguê<PERSON>", "com_nav_lang_chinese": "中文", "com_nav_lang_dutch": "Nederlands", "com_nav_lang_english": "English", "com_nav_lang_estonian": "<PERSON><PERSON><PERSON> keel", "com_nav_lang_finnish": "<PERSON><PERSON>", "com_nav_lang_french": "Français ", "com_nav_lang_georgian": "<PERSON><PERSON>", "com_nav_lang_german": "De<PERSON>ch", "com_nav_lang_hebrew": "עברית", "com_nav_lang_indonesia": "Indonesia", "com_nav_lang_italian": "Italiano", "com_nav_lang_japanese": "日本語", "com_nav_lang_korean": "한국어", "com_nav_lang_polish": "<PERSON><PERSON>", "com_nav_lang_portuguese": "Português", "com_nav_lang_russian": "Русский", "com_nav_lang_spanish": "Español", "com_nav_lang_swedish": "Svenska", "com_nav_lang_thai": "Tailandês", "com_nav_lang_traditional_chinese": "繁體中文", "com_nav_lang_turkish": "Türkçe", "com_nav_lang_vietnamese": "Tiếng <PERSON>", "com_nav_language": "Idioma", "com_nav_latex_parsing": "Análise de LaTeX em mensagens (pode afetar o desempenho)", "com_nav_log_out": "<PERSON><PERSON>", "com_nav_long_audio_warning": "Textos mais longos levarão mais tempo para processar.", "com_nav_maximize_chat_space": "Maximizar o espaço de conversa", "com_nav_modular_chat": "Habilitar troca de Endpoints no meio da conversa", "com_nav_my_files": "<PERSON><PERSON>", "com_nav_not_supported": "Não Suportado", "com_nav_open_sidebar": "Abrir barra lateral", "com_nav_playback_rate": "Taxa de Reprodução de Áudio", "com_nav_plugin_auth_error": "Houve um erro ao tentar autenticar este plugin. Por favor, tente novamente.", "com_nav_plugin_install": "Instalar", "com_nav_plugin_search": "Buscar plugins", "com_nav_plugin_store": "<PERSON><PERSON>", "com_nav_plugin_uninstall": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_plus_command": "Comando +", "com_nav_plus_command_description": "Alternar comando \"+\" para adicionar uma configuração de resposta múltipla", "com_nav_profile_picture": "Foto de Perfil", "com_nav_save_drafts": "Salvar rascunhos localmente", "com_nav_scroll_button": "Ir para o botão final", "com_nav_search_placeholder": "Buscar mensagens", "com_nav_send_message": "Enviar mensagem", "com_nav_setting_account": "Conta", "com_nav_setting_beta": "Recursos beta", "com_nav_setting_chat": "Cha<PERSON>", "com_nav_setting_data": "Controles de dados", "com_nav_setting_general": "G<PERSON>", "com_nav_setting_speech": "Fala", "com_nav_settings": "Configurações", "com_nav_shared_links": "Links compartilhados", "com_nav_show_code": "Sempre mostrar código ao usar o interpretador de código", "com_nav_show_thinking": "Menus suspensos de pensamento aberto por padrão", "com_nav_slash_command": "Comando /", "com_nav_slash_command_description": "Alternar comando \"/\" para selecionar um prompt via teclado", "com_nav_speech_to_text": "Fala para Texto", "com_nav_stop_generating": "<PERSON><PERSON> de gera<PERSON>", "com_nav_text_to_speech": "Texto para Fala", "com_nav_theme": "<PERSON><PERSON>", "com_nav_theme_dark": "Escuro", "com_nav_theme_light": "<PERSON><PERSON><PERSON>", "com_nav_theme_system": "Sistema", "com_nav_tool_dialog": "Ferramentas do Assistente", "com_nav_tool_dialog_agents": "Ferramentas do agente", "com_nav_tool_dialog_description": "O assistente deve ser salvo para persistir as seleções de ferramentas.", "com_nav_tool_remove": "Remover", "com_nav_tool_search": "Buscar ferramentas", "com_nav_user": "USUÁRIO", "com_nav_user_msg_markdown": "Renderizar mensagens do usuário como favoritos", "com_nav_user_name_display": "Exibir nome de usuário nas mensagens", "com_nav_voice_select": "Voz", "com_show_agent_settings": "Mostrar Configurações do Agente", "com_show_completion_settings": "Mostrar Configurações de Conclusão", "com_show_examples": "Mostrar Exemplos", "com_sidepanel_agent_builder": "Con<PERSON><PERSON><PERSON> de Agente", "com_sidepanel_assistant_builder": "<PERSON><PERSON><PERSON><PERSON>", "com_sidepanel_attach_files": "Anexar Arquivos", "com_sidepanel_conversation_tags": "Marcadores", "com_sidepanel_hide_panel": "<PERSON><PERSON><PERSON><PERSON>", "com_sidepanel_manage_files": "Gerenciar Arquivos", "com_sidepanel_parameters": "Parâmetros", "com_ui_2fa_account_security": "A autenticação de dois fatores acrescenta uma camada extra de segurança à sua conta", "com_ui_2fa_disable": "Desabilitar 2FA", "com_ui_2fa_disable_error": "Ocorreu um erro ao desativar a autenticação de dois fatores", "com_ui_2fa_disabled": "A 2FA foi desativada", "com_ui_2fa_enable": "Ativar 2FA", "com_ui_2fa_enabled": "A 2FA foi ativada", "com_ui_2fa_generate_error": "Ocorreu um erro ao gerar as configurações de autenticação de dois fatores", "com_ui_2fa_invalid": "Código de autenticação de dois fatores inválido", "com_ui_2fa_setup": "Configurar 2FA", "com_ui_2fa_verified": "Autenticação de dois fatores verificada com sucesso", "com_ui_accept": "<PERSON><PERSON> aceito", "com_ui_add": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_add_model_preset": "Adicionar um modelo ou predefinição para uma resposta adicional", "com_ui_add_multi_conversation": "Adicionar multi-conversação", "com_ui_admin": "Admin", "com_ui_admin_access_warning": "Desabilitar o acesso de Admin a esse recurso pode causar problemas inesperados na IU que exigem atualização. Se salvo, a única maneira de reverter é por meio da configuração de interface na configuração librechat.yaml que afeta todas as funções.", "com_ui_admin_settings": "Configurações de Admin", "com_ui_advanced": "Avançado", "com_ui_agent": "<PERSON><PERSON>", "com_ui_agent_delete_error": "Houve um erro ao excluir o agente", "com_ui_agent_deleted": "Agente excluído com sucesso", "com_ui_agent_duplicate_error": "Ocorreu um erro ao duplicar o agente", "com_ui_agent_duplicated": "Agente duplicado com sucesso", "com_ui_agent_editing_allowed": "Outros usuários já podem editar este agente", "com_ui_agent_shared_to_all": "algo precisa ir aqui. esta vazio", "com_ui_agents": "<PERSON><PERSON>", "com_ui_agents_allow_create": "Permitir a criação de agentes", "com_ui_agents_allow_share_global": "Permitir compartilhamento de agentes para todos os usuários", "com_ui_agents_allow_use": "Permitir o uso de agentes", "com_ui_all": "todos", "com_ui_all_proper": "Todos", "com_ui_analyzing": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_analyzing_finished": "<PERSON><PERSON><PERSON><PERSON> con<PERSON>", "com_ui_api_key": "Chave API", "com_ui_archive": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_archive_error": "Falha ao arquivar conversa", "com_ui_artifact_click": "Clique para abrir", "com_ui_artifacts": "Arte<PERSON><PERSON>", "com_ui_artifacts_toggle": "Alternar UI de Artefatos", "com_ui_artifacts_toggle_agent": "Habilitar artefatos", "com_ui_ascending": "Asc", "com_ui_assistant": "<PERSON><PERSON><PERSON>", "com_ui_assistant_delete_error": "Houve um erro ao excluir o assistente", "com_ui_assistant_deleted": "Assistente excluído com sucesso", "com_ui_assistants": "Assistentes", "com_ui_assistants_output": "<PERSON><PERSON><PERSON> dos Assistentes", "com_ui_attach_error": "Não é possível anexar o arquivo. Crie ou selecione uma conversa, ou tente atualizar a página.", "com_ui_attach_error_openai": "Não é possível anexar arquivos de Assistente a outros endpoints", "com_ui_attach_error_size": "Limite de tamanho de arquivo excedido para o endpoint:", "com_ui_attach_error_type": "Tipo de arquivo não suportado para o endpoint:", "com_ui_attach_warn_endpoint": "Arquivos não compatíveis podem ser ignorados sem uma ferramenta compatível", "com_ui_attachment": "Anexo", "com_ui_auth_type": "Tipo de autenticação", "com_ui_auth_url": "URL de autorização", "com_ui_authentication": "Autenticação", "com_ui_authentication_type": "Tipo de Autenticação", "com_ui_avatar": "Avatar", "com_ui_azure": "Azure", "com_ui_back_to_chat": "Voltar ao Chat", "com_ui_back_to_prompts": "Voltar aos Prompts", "com_ui_backup_codes": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_backup_codes_regenerate_error": "Ocorreu um erro ao regerar os códigos de backup", "com_ui_backup_codes_regenerated": "Os códigos de backup foram regerados com sucesso", "com_ui_basic": "Básico", "com_ui_basic_auth_header": "Cabeçalho de autorização básico", "com_ui_bearer": "Portador", "com_ui_bookmark_delete_confirm": "Tem certeza de que deseja excluir este favorito?", "com_ui_bookmarks": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_bookmarks_add": "Adicionar marcador<PERSON>", "com_ui_bookmarks_add_to_conversation": "Adicionar à conversa atual", "com_ui_bookmarks_count": "Contagem", "com_ui_bookmarks_create_error": "Houve um erro ao criar o favorito", "com_ui_bookmarks_create_exists": "Este favorito já existe", "com_ui_bookmarks_create_success": "Favorito criado com sucesso", "com_ui_bookmarks_delete": "Deletar marcadores", "com_ui_bookmarks_delete_error": "Houve um erro ao excluir o favorito", "com_ui_bookmarks_delete_success": "Favorito excluído com sucesso", "com_ui_bookmarks_description": "Descrição", "com_ui_bookmarks_edit": "<PERSON>ar marcador<PERSON>", "com_ui_bookmarks_filter": "Filtrar favoritos...", "com_ui_bookmarks_new": "Novo Favorito", "com_ui_bookmarks_title": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_bookmarks_update_error": "Houve um erro ao atualizar o favorito", "com_ui_bookmarks_update_success": "Favorito atualizado com sucesso", "com_ui_bulk_delete_error": "Falha ao excluir links compartilhados", "com_ui_callback_url": "URL de retorno de chamada", "com_ui_cancel": "<PERSON><PERSON><PERSON>", "com_ui_chat": "Cha<PERSON>", "com_ui_chat_history": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_clear": "Limpar", "com_ui_clear_all": "<PERSON><PERSON> tudo", "com_ui_client_id": "ID do cliente", "com_ui_client_secret": "Segredo do cliente", "com_ui_close": "<PERSON><PERSON><PERSON>", "com_ui_close_menu": "<PERSON><PERSON><PERSON>", "com_ui_code": "Código", "com_ui_collapse_chat": "<PERSON><PERSON><PERSON><PERSON> bate-papo", "com_ui_command_placeholder": "Opcional: Insira um comando para o prompt ou o nome será usado.", "com_ui_command_usage_placeholder": "Selecione um Prompt por comando ou nome", "com_ui_complete_setup": "Configuração completa", "com_ui_confirm_action": "Confirmar <PERSON>", "com_ui_confirm_admin_use_change": "Alterar esta configuração bloqueará o acesso para administradores, incluindo você. Tem certeza de que deseja prosseguir?", "com_ui_confirm_change": "Confirmar alter<PERSON>", "com_ui_context": "Contexto", "com_ui_continue": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_controls": "Controles", "com_ui_copied": "Copiado!", "com_ui_copied_to_clipboard": "Copiado para a área de transferência", "com_ui_copy_code": "<PERSON><PERSON>r c<PERSON>", "com_ui_copy_link": "Copiar link", "com_ui_copy_to_clipboard": "Copiar para a área de transferência", "com_ui_create": "<PERSON><PERSON><PERSON>", "com_ui_create_link": "Criar link", "com_ui_create_prompt": "<PERSON><PERSON><PERSON> Prompt", "com_ui_currently_production": "Atualmente em produção", "com_ui_custom": "Personalizado", "com_ui_custom_header_name": "Nome do cabeçalho personalizado", "com_ui_custom_prompt_mode": "Modo de Prompt Personalizado", "com_ui_dashboard": "<PERSON><PERSON>", "com_ui_date": "Data", "com_ui_date_april": "Abril", "com_ui_date_august": "Agosto", "com_ui_date_december": "Dezembro", "com_ui_date_february": "<PERSON><PERSON>", "com_ui_date_january": "Janeiro", "com_ui_date_july": "<PERSON><PERSON>", "com_ui_date_june": "<PERSON><PERSON>", "com_ui_date_march": "Março", "com_ui_date_may": "<PERSON><PERSON>", "com_ui_date_november": "Novembro", "com_ui_date_october": "Out<PERSON>ro", "com_ui_date_previous_30_days": "Últimos 30 dias", "com_ui_date_previous_7_days": "Últimos 7 dias", "com_ui_date_september": "Setembro", "com_ui_date_today": "Hoje", "com_ui_date_yesterday": "Ontem", "com_ui_decline": "Eu não aceito", "com_ui_default_post_request": "Padrão (solicitação POST)", "com_ui_delete": "Excluir", "com_ui_delete_action": "Excluir Ação", "com_ui_delete_action_confirm": "Tem certeza de que deseja excluir esta ação?", "com_ui_delete_agent_confirm": "Tem certeza de que deseja excluir este agente?", "com_ui_delete_assistant_confirm": "Tem certeza de que deseja excluir este Assistente? Isso não pode ser desfeito.", "com_ui_delete_confirm": "Is<PERSON> exclu<PERSON>", "com_ui_delete_confirm_prompt_version_var": "<PERSON><PERSON> excluirá a versão selecionada para \"{{0}}\". Se não houver outras versões, o prompt será excluído.", "com_ui_delete_conversation": "Excluir chat?", "com_ui_delete_prompt": "Excluir Prompt?", "com_ui_delete_shared_link": "Excluir link compartilhado?", "com_ui_delete_tool": "Excluir Ferramenta", "com_ui_delete_tool_confirm": "Tem certeza de que deseja excluir esta ferramenta?", "com_ui_descending": "Desc", "com_ui_description": "Descrição", "com_ui_description_placeholder": "Opcional: Insira uma descrição para exibir para o prompt", "com_ui_disabling": "Desativando...", "com_ui_download": "Download", "com_ui_download_artifact": "Download artefato", "com_ui_download_backup": "Baixar códigos de backup", "com_ui_download_backup_tooltip": "Antes de continuar, baixe seus códigos de backup. Você precisará deles para recuperar o acesso se perder seu dispositivo autenticador", "com_ui_download_error": "Erro ao baixar o arquivo. O arquivo pode ter sido excluído.", "com_ui_drag_drop": "algo precisa ir aqui. estava vazio", "com_ui_dropdown_variables": "Variáveis de dropdown:", "com_ui_dropdown_variables_info": "Crie menus dropdown personalizados para seus prompts: `{{nome_da_variável:opção1|opção2|opção3}}`", "com_ui_duplicate": "Dup<PERSON><PERSON>", "com_ui_duplication_error": "Ocorreu um erro ao duplicar a conversa", "com_ui_duplication_processing": "Duplicando conversa...", "com_ui_duplication_success": "Conversa duplicada com sucesso", "com_ui_edit": "<PERSON><PERSON>", "com_ui_empty_category": "-", "com_ui_endpoint": "Endpoint", "com_ui_endpoint_menu": "Menu endpoint LLM", "com_ui_enter": "Entrar", "com_ui_enter_api_key": "Insira a chave da API", "com_ui_enter_openapi_schema": "Insira seu esquema OpenAPI aqui", "com_ui_error": "Erro", "com_ui_error_connection": "Erro ao conectar ao servidor, tente atualizar a página.", "com_ui_error_save_admin_settings": "Houve um erro ao salvar suas configurações de admin.", "com_ui_examples": "Exemplos", "com_ui_export_convo_modal": "Exportar Modal de Conversação", "com_ui_field_required": "Este campo é obrigatório", "com_ui_filter_prompts": "Filtrar prompts", "com_ui_filter_prompts_name": "Filtrar prompts por nome", "com_ui_finance": "Financiar", "com_ui_fork": "Bifurcar", "com_ui_fork_all_target": "Incluir todos para/de aqui", "com_ui_fork_branches": "Incluir ramificações relacionadas", "com_ui_fork_change_default": "Opção de bifurcação padrão", "com_ui_fork_default": "Usar opção de bifurcação padrão", "com_ui_fork_error": "Houve um erro ao bifurcar a conversa", "com_ui_fork_from_message": "Selecione uma opção de bifurcação", "com_ui_fork_info_1": "Use esta configuração para bifurcar mensagens com o comportamento desejado.", "com_ui_fork_info_2": "\"Bifurcação\" refere-se à criação de uma nova conversa que começa/termina a partir de mensagens específicas na conversa atual, criando uma cópia de acordo com as opções selecionadas.", "com_ui_fork_info_3": "A \"mensagem alvo\" refere-se à mensagem da qual este popup foi aberto, ou, se você marcar \"{{0}}\", a última mensagem na conversa.", "com_ui_fork_info_branches": "Esta opção bifurca as mensagens visíveis, junto com ramificações relacionadas; em outras palavras, o caminho direto para a mensagem alvo, incluindo ramificações ao longo do caminho.", "com_ui_fork_info_remember": "<PERSON><PERSON> isto para lembrar as opções que você seleciona para uso futuro, tornando mais rápido bifurcar conversas conforme preferido.", "com_ui_fork_info_start": "Se marcado, a bifurcação começará desta mensagem até a última mensagem na conversa, de acordo com o comportamento selecionado acima.", "com_ui_fork_info_target": "Esta opção bifurca todas as mensagens até a mensagem alvo, incluindo seus vizinhos; em outras palavras, todos os ramos de mensagens, estejam ou não visíveis ou ao longo do mesmo caminho, estão incluídos.", "com_ui_fork_info_visible": "Esta opção bifurca apenas as mensagens visíveis; em outras palavras, o caminho direto para a mensagem alvo, sem quaisquer ramificações.", "com_ui_fork_processing": "Bifurcando conversa...", "com_ui_fork_remember": "<PERSON><PERSON><PERSON>", "com_ui_fork_remember_checked": "Sua seleção será lembrada após o uso. Altere isso a qualquer momento nas configurações.", "com_ui_fork_split_target": "Iniciar bifurcação aqui", "com_ui_fork_split_target_setting": "Iniciar bifurcação a partir da mensagem alvo por padrão", "com_ui_fork_success": "Conversa bifurcada com sucesso", "com_ui_fork_visible": "Apenas mensagens visíveis", "com_ui_generate_backup": "<PERSON><PERSON><PERSON> de <PERSON>", "com_ui_generate_qrcode": "Gerar QR Code", "com_ui_generating": "Gerando...", "com_ui_global_group": "algo precisa ir aqui. estava vazio", "com_ui_go_back": "Volte", "com_ui_go_to_conversation": "Ir para a conversa", "com_ui_happy_birthday": "É meu 1º aniversário!", "com_ui_hide_qr": "Ocultar QR Code", "com_ui_host": "Host", "com_ui_idea": "<PERSON><PERSON><PERSON>", "com_ui_image_gen": "Geração de Imagem", "com_ui_import": "Importar", "com_ui_import_conversation_error": "Houve um erro ao importar suas conversas", "com_ui_import_conversation_file_type_error": "Tipo de importação não suportado", "com_ui_import_conversation_info": "Importar conversas de um arquivo JSON", "com_ui_import_conversation_success": "Conversas importadas com sucesso", "com_ui_include_shadcnui": "Incluir instruções de componentes shadcn/ui", "com_ui_input": "Entrada", "com_ui_instructions": "Instruções", "com_ui_latest_footer": "Toda IA para Todos.", "com_ui_latest_production_version": "Última versão de produção", "com_ui_latest_version": "<PERSON><PERSON><PERSON> versão", "com_ui_librechat_code_api_key": "Obtenha sua chave de API do LibreChat Code Interpreter", "com_ui_librechat_code_api_subtitle": "Seguro. Multi-idioma. Arquivos de entrada/saída.", "com_ui_librechat_code_api_title": "Execute o código AI", "com_ui_loading": "Carregando", "com_ui_locked": "Bloqueado", "com_ui_logo": "{{0}} Logo", "com_ui_manage": "Gerenciar", "com_ui_max_tags": "O número máximo permitido é {{0}}, usando os valores mais recentes.", "com_ui_mention": "<PERSON><PERSON><PERSON> um endpoint, assistente ou predefinição para alternar rapidamente para ele", "com_ui_min_tags": "Não é possível remover mais valores, um mínimo de {{0}} é necessário.", "com_ui_misc": "Diversos", "com_ui_model": "<PERSON><PERSON>", "com_ui_model_parameters": "Parâmetros do Modelo", "com_ui_more_info": "Mais informaç<PERSON>", "com_ui_my_prompts": "Meus Prompts", "com_ui_name": "Nome", "com_ui_new": "Novo", "com_ui_new_chat": "Novo chat", "com_ui_next": "Próximo", "com_ui_no": "Não", "com_ui_no_backup_codes": "Nenhum código de backup disponível. Por favor, gere novos", "com_ui_no_bookmarks": "Parece que você ainda não tem favoritos. Clique em um chat e adicione um novo", "com_ui_no_category": "Sem categoria", "com_ui_no_changes": "Sem alterações para atualizar", "com_ui_no_data": "algo precisa ir aqui. estava vazio", "com_ui_no_terms_content": "Nenhum conteúdo de termos e condições para exibir", "com_ui_no_valid_items": "algo precisa ir aqui. estava vazio", "com_ui_none": "<PERSON><PERSON><PERSON>", "com_ui_not_used": "<PERSON>ão usado", "com_ui_nothing_found": "<PERSON>da encontrado", "com_ui_oauth": "OAuth", "com_ui_of": "de", "com_ui_off": "Des<PERSON><PERSON>", "com_ui_on": "Ligado", "com_ui_openai": "OpenAI", "com_ui_page": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_prev": "Anterior", "com_ui_preview": "Pré-visualizar", "com_ui_privacy_policy": "Política de Privacidade", "com_ui_privacy_policy_url": "URL da Política de Privacidade", "com_ui_prompt": "Prompt", "com_ui_prompt_already_shared_to_all": "Este prompt já está compartilhado com todos os usuários", "com_ui_prompt_name": "Nome do Prompt", "com_ui_prompt_name_required": "Nome do Prompt é obrigatório", "com_ui_prompt_preview_not_shared": "O autor não permitiu colaboração para este prompt.", "com_ui_prompt_text": "Texto", "com_ui_prompt_text_required": "Texto é obrigatório", "com_ui_prompt_update_error": "Houve um erro ao atualizar o prompt", "com_ui_prompts": "Prompts", "com_ui_prompts_allow_create": "Permitir c<PERSON> de Prompts", "com_ui_prompts_allow_share_global": "Permitir compartilhamento de Prompts com todos os usuários", "com_ui_prompts_allow_use": "<PERSON><PERSON><PERSON> uso de Prompts", "com_ui_provider": "<PERSON><PERSON><PERSON>", "com_ui_read_aloud": "Ler em voz alta", "com_ui_refresh_link": "Atualizar link", "com_ui_regenerate": "<PERSON><PERSON><PERSON>", "com_ui_regenerate_backup": "Regerar código de backup", "com_ui_regenerating": "Regerando...", "com_ui_region": "Região", "com_ui_rename": "Renomear", "com_ui_rename_prompt": "Renomear prompt", "com_ui_requires_auth": "<PERSON>quer auten<PERSON>", "com_ui_reset_var": "Redefinir {{0}}", "com_ui_result": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_revoke": "<PERSON><PERSON><PERSON>", "com_ui_revoke_info": "<PERSON><PERSON><PERSON> as credenciais fornecidas pelo usuário", "com_ui_revoke_key_confirm": "Tem certeza de que deseja revogar esta chave?", "com_ui_revoke_key_endpoint": "<PERSON>oga<PERSON> chave para {{0}}", "com_ui_revoke_keys": "<PERSON>ogar chaves", "com_ui_revoke_keys_confirm": "Tem certeza de que deseja revogar todas as chaves?", "com_ui_role_select": "Papel", "com_ui_roleplay": "RPG", "com_ui_run_code": "Executar código", "com_ui_run_code_error": "Ocorreu um erro ao executar o código", "com_ui_save": "<PERSON><PERSON>", "com_ui_save_submit": "Salvar & Enviar", "com_ui_saved": "Salvo!", "com_ui_schema": "Esquema", "com_ui_scope": "Escopo", "com_ui_search": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_secret_key": "Chave secreta", "com_ui_select": "Selecionar", "com_ui_select_file": "Selecionar um arquivo", "com_ui_select_model": "Selecionar um modelo", "com_ui_select_provider": "Selecionar um provedor", "com_ui_select_provider_first": "Selecione um provedor primeiro", "com_ui_select_region": "Selecione uma região", "com_ui_select_search_model": "Pesquisar modelo por nome", "com_ui_select_search_plugin": "Pesquisar plugin por nome", "com_ui_select_search_provider": "<PERSON><PERSON><PERSON><PERSON> provedor por nome", "com_ui_select_search_region": "Pesquisar região por nome", "com_ui_share": "Compartilhar", "com_ui_share_create_message": "Seu nome e quaisquer mensagens que você adicionar após o compartilhamento permanecerão privadas.", "com_ui_share_delete_error": "Houve um erro ao excluir o link compartilhado", "com_ui_share_error": "Houve um erro ao compartilhar o link do chat", "com_ui_share_form_description": "algo precisa ir aqui. esta vazio", "com_ui_share_link_to_chat": "Compartilhar link para o chat", "com_ui_share_to_all_users": "Compartilhar com todos os usuários", "com_ui_share_update_message": "Seu nome, instruções personalizadas e quaisquer mensagens que você adicionar após o compartilhamento permanecerão privadas.", "com_ui_share_var": "Compartilhar {{0}}", "com_ui_shared_link_bulk_delete_success": "Links compartilhados excluídos com sucesso", "com_ui_shared_link_delete_success": "Link compartilhado excluído com sucesso", "com_ui_shared_link_not_found": "Link compartilhado não encontrado", "com_ui_shared_prompts": "Prompts <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_shop": "Shopping", "com_ui_show": "Mostrar", "com_ui_show_all": "<PERSON><PERSON>", "com_ui_show_qr": "Mostrar QR Code", "com_ui_sign_in_to_domain": "Entre em {{0}}", "com_ui_simple": "Simples", "com_ui_size": "<PERSON><PERSON><PERSON>", "com_ui_special_variables": "Variáveis especiais:", "com_ui_speech_while_submitting": "Não é possível enviar a fala enquanto uma resposta está sendo gerada", "com_ui_stop": "<PERSON><PERSON>", "com_ui_storage": "Armazenamento", "com_ui_submit": "Enviar", "com_ui_teach_or_explain": "Aprendizado", "com_ui_terms_and_conditions": "Termos e Condições", "com_ui_terms_of_service": "Termos de Serviço", "com_ui_thinking": "Pensando...", "com_ui_thoughts": "Pensamentos", "com_ui_token_exchange_method": "Método de troca de tokens", "com_ui_token_url": "URL do token", "com_ui_tools": "Ferramentas", "com_ui_travel": "Viagem", "com_ui_unarchive": "Desar<PERSON><PERSON>", "com_ui_unarchive_error": "Falha ao desarquivar conversa", "com_ui_unknown": "Desconhecido", "com_ui_update": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_upload": "<PERSON><PERSON><PERSON>", "com_ui_upload_code_files": "Carregar para o interpretador de código", "com_ui_upload_delay": "O upload de \"{{0}}\" está demorando mais do que o esperado. Por favor, aguarde enquanto o arquivo termina de ser indexado para recuperação.", "com_ui_upload_error": "Houve um erro ao carregar seu arquivo", "com_ui_upload_file_context": "Contexto de upload de arquivo", "com_ui_upload_file_search": "Upload para pesquisa de arquivos", "com_ui_upload_files": "<PERSON><PERSON><PERSON>", "com_ui_upload_image": "<PERSON>egar uma imagem", "com_ui_upload_image_input": "Upload de imagem", "com_ui_upload_invalid": "Arquivo inválido para upload. Deve ser uma imagem não excedendo o limite", "com_ui_upload_invalid_var": "Arquivo inválido para upload. Deve ser uma imagem não excedendo {{0}} MB", "com_ui_upload_ocr_text": "Carregar como texto", "com_ui_upload_success": "Arquivo carregado com sucesso", "com_ui_upload_type": "Selecione o tipo de upload", "com_ui_use_2fa_code": "Use o código 2FA em vez disso", "com_ui_use_backup_code": "Use o código de backup", "com_ui_use_micrphone": "Usar microfone", "com_ui_use_prompt": "Usar prompt", "com_ui_used": "Usado", "com_ui_variables": "Variáveis", "com_ui_variables_info": "Use chaves duplas no seu texto para criar variáveis, por exemplo, `{{exemplo de variável}}`, para preencher posteriormente ao usar o prompt.", "com_ui_verify": "Verificar", "com_ui_version_var": "<PERSON><PERSON><PERSON> {{0}}", "com_ui_versions": "Versõ<PERSON>", "com_ui_view_source": "Ver chat de origem", "com_ui_write": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_yes": "<PERSON>m", "com_ui_zoom": "Zoom", "com_user_message": "Você", "com_endpoint_search_endpoint_models": "Procurar {{0}} modelos...", "com_endpoint_search_models": "Procurar modelos...", "com_endpoint_search_var": "Procurar {{0}}...", "com_error_heic_conversion": "Falha ao converter imagem HEIC para JPEG. Por favor, tente converter a imagem manualmente ou use um formato diferente.", "com_error_invalid_agent_provider": "O provedor \"{{0}}\" não está disponível para uso com Agentes. Por favor, vá para as configurações do seu agente e selecione um provedor atualmente disponível.", "com_info_heic_converting": "Convertendo imagem HEIC para JPEG...", "com_nav_balance_auto_refill_disabled": "Auto-Refill está desativado.", "com_nav_balance_auto_refill_error": "Erro ao carregar configurações de auto-refill.", "com_nav_balance_auto_refill_settings": "Configurações de Auto-Refill", "com_nav_balance_day": "dia", "com_nav_balance_days": "dias", "com_nav_balance_every": "<PERSON><PERSON>", "com_nav_balance_hour": "hora", "com_nav_balance_hours": "horas", "com_nav_balance_interval": "Intervalo:", "com_nav_balance_last_refill": "Último <PERSON>:", "com_nav_balance_minute": "minuto", "com_nav_balance_minutes": "minutos", "com_nav_balance_month": "mês", "com_nav_balance_months": "meses", "com_nav_balance_next_refill": "Próximo Refill:", "com_nav_balance_next_refill_info": "O próximo refill ocorrerá automaticamente apenas quando ambas as condições forem atendidas: o intervalo de tempo designado tiver passado desde o último refill, e enviar um prompt fará com que seu saldo caia abaixo de zero.", "com_nav_balance_refill_amount": "Quantidade de Refill:", "com_nav_balance_second": "segundo", "com_nav_balance_seconds": "segundos", "com_nav_balance_week": "semana", "com_nav_balance_weeks": "semanas", "com_nav_center_chat_input": "Centralizar entrada de chat na tela de boas-vindas", "com_nav_edit_chat_badges": "Editar insígnias de conversa", "com_nav_info_balance": "O saldo mostra quantos créditos de token você tem para usar. Os créditos de token correspondem a um valor monetário (por exemplo, 1000 créditos = $0.001 USD)", "com_nav_info_save_badges_state": "<PERSON>uando habilitado, o estado das insígnias de conversa será salvo. Isso significa que, se você criar uma nova conversa, as insígnias permanecerão no mesmo estado que a conversa anterior. Se você desabilitar esta opção, as insígnias serão redefinidas para o estado padrão toda vez que você criar uma nova conversa.", "com_nav_lang_catalan": "Català", "com_nav_lang_czech": "Čeština", "com_nav_lang_danish": "Dansk", "com_nav_lang_hungarian": "<PERSON><PERSON><PERSON>", "com_nav_mcp_vars_update_error": "Erro ao atualizar variáveis de usuário MCP: {{0}}", "com_nav_mcp_vars_updated": "Variáveis de usuário MCP atualizadas com sucesso.", "com_nav_save_badges_state": "Salvar estado das insígnias de conversa", "com_nav_setting_balance": "<PERSON><PERSON>", "com_nav_setting_mcp": "Configurações do MCP", "com_nav_setting_personalization": "Personalização", "com_sidepanel_mcp_enter_value": "Insira o valor para {{0}}", "com_sidepanel_mcp_no_servers_with_vars": "Nenhum servidor MCP com variáveis configuráveis.", "com_sidepanel_mcp_variables_for": "Variáveis MCP para {{0}}", "com_sources_image_alt": "Resultado da busca de imagem", "com_sources_more_sources": "+{{count}} fontes", "com_sources_tab_all": "<PERSON><PERSON>", "com_sources_tab_images": "Imagens", "com_sources_tab_news": "Notícias", "com_sources_title": "<PERSON><PERSON><PERSON>", "com_ui_action_button": "Botão de Ação", "com_ui_add_mcp": "Ad<PERSON>onar <PERSON>", "com_ui_add_mcp_server": "Adicionar servidor <PERSON>", "com_ui_adding_details": "<PERSON><PERSON><PERSON><PERSON> de<PERSON>", "com_ui_advanced_settings": "Configurações Avançadas", "com_ui_agent_chain": "<PERSON><PERSON> (Mixture-of-Agents)", "com_ui_agent_chain_info": "Permite a criação de sequências de agentes. Cada agente pode acessar as saídas dos agentes anteriores na cadeia. Baseado na arquitetura \"Mixture-of-Agents\", onde os agentes usam as saídas anteriores como informações auxiliares.", "com_ui_agent_chain_max": "Voc<PERSON> atingiu o máximo de {{0}} agentes.", "com_ui_agent_recursion_limit": "Limite de recursão do agente", "com_ui_agent_recursion_limit_info": "Limita quantos passos o agente pode executar em uma execução antes de fornecer uma resposta final. O padrão é 25 passos. Um passo é uma solicitação de API de IA ou uma rodada de uso de ferramenta. Por exemplo, uma interação básica com uma ferramenta requer 3 passos: solicitação inicial, uso da ferramenta e solicitação de acompanhamento.", "com_ui_agent_var": "{{0}} agente", "com_ui_agent_version": "Vers<PERSON>", "com_ui_agent_version_active": "Versão Ativa", "com_ui_agent_version_duplicate": "Versão duplicada detectada. Isso criaria uma versão idêntica à versão {{versionIndex}}.", "com_ui_agent_version_empty": "Nenhuma versão disponível", "com_ui_agent_version_error": "Erro ao buscar versões", "com_ui_agent_version_history": "Histórico de Versões", "com_ui_agent_version_no_agent": "Nenhum agente selecionado. Por favor, selecione um agente para visualizar o histórico de versões.", "com_ui_agent_version_no_date": "Data não disponível", "com_ui_agent_version_restore": "Restaurar", "com_ui_agent_version_restore_confirm": "Tem certeza de que deseja restaurar esta versão?", "com_ui_agent_version_restore_error": "Erro ao restaurar versão", "com_ui_agent_version_restore_success": "Versão restaurada com sucesso", "com_ui_agent_version_title": "<PERSON>ers<PERSON> {{versionNumber}}", "com_ui_agent_version_unknown_date": "Data desconhecida", "com_ui_archive_delete_error": "Falha ao excluir conversa arquivada", "com_ui_attach_remove": "Remover arquivo", "com_ui_available_tools": "Ferramentas disponíveis", "com_ui_back": "Voltar", "com_ui_cancelled": "Cancelado", "com_ui_category": "Categoria", "com_ui_close_window": "<PERSON><PERSON><PERSON>", "com_ui_configure_mcp_variables_for": "Configurar variáveis para {{0}}", "com_ui_convo_delete_error": "Falha ao excluir conversa", "com_ui_create_memory": "<PERSON><PERSON><PERSON>", "com_ui_creating_image": "Criando imagem. <PERSON><PERSON> pode demorar um pouco.", "com_ui_current": "Atual", "com_ui_delete_mcp": "Deletar MCP", "com_ui_delete_mcp_confirm": "Tem certeza de que deseja excluir este servidor MCP?", "com_ui_delete_mcp_error": "Falha ao excluir servidor MCP", "com_ui_delete_mcp_success": "MCP excluído com sucesso", "com_ui_delete_memory": "Excluir memória?", "com_ui_deleted": "Excluído", "com_ui_deselect_all": "Deselecionar tudo", "com_ui_edit_editing_image": "<PERSON><PERSON><PERSON> imagem", "com_ui_edit_mcp_server": "<PERSON><PERSON> servidor <PERSON>", "com_ui_edit_memory": "<PERSON><PERSON>", "com_ui_enter_key": "Insira a chave", "com_ui_enter_value": "Insira o valor", "com_ui_error_updating_preferences": "Erro ao atualizar preferências", "com_ui_expand_chat": "<PERSON>pan<PERSON> bate-papo", "com_ui_feedback_more": "Mais...", "com_ui_feedback_more_information": "Forneça mais informações", "com_ui_feedback_negative": "Precisa de melhoria", "com_ui_feedback_placeholder": "Forneça mais informações", "com_ui_feedback_positive": "Excelente", "com_ui_feedback_tag_accurate_reliable": "Acurato e confiável", "com_ui_feedback_tag_attention_to_detail": "Presta atenção aos detalhes", "com_ui_feedback_tag_bad_style": "<PERSON><PERSON><PERSON>", "com_ui_feedback_tag_clear_well_written": "Claro e bem escrito", "com_ui_feedback_tag_creative_solution": "Solução criativa", "com_ui_feedback_tag_inaccurate": "Incorreto", "com_ui_feedback_tag_missing_image": "E<PERSON>ava uma imagem", "com_ui_feedback_tag_not_helpful": "Não foi útil", "com_ui_feedback_tag_not_matched": "Não correspondeu à minha solicitação", "com_ui_feedback_tag_other": "Outro problema", "com_ui_feedback_tag_unjustified_refusal": "Recusou sem motivo", "com_ui_file_size": "Tamanho do arquivo", "com_ui_files": "<PERSON>r<PERSON><PERSON>", "com_ui_final_touch": "Toque final", "com_ui_fork_info_button_label": "Ver informações sobre bifurcação de conversas", "com_ui_fork_more_details_about": "Ver informações adicionais e detalhes sobre a opção de bifurcação \"{{0}}\"", "com_ui_fork_more_info_options": "Ver explicação detalhada de todas as opções de bifurcação e seus comportamentos", "com_ui_generation_settings": "Configurações de geração", "com_ui_getting_started": "Começando", "com_ui_good_afternoon": "<PERSON>a tarde", "com_ui_good_evening": "Boa noite", "com_ui_good_morning": "Bom dia", "com_ui_hide_image_details": "<PERSON><PERSON><PERSON><PERSON> de<PERSON><PERSON> da <PERSON>m", "com_ui_icon": "Ícone", "com_ui_image_created": "<PERSON><PERSON> criada", "com_ui_image_details": "<PERSON><PERSON><PERSON> da <PERSON>m", "com_ui_image_edited": "Imagem editada", "com_ui_key": "Chave", "com_ui_late_night": "Boa noite", "com_ui_mcp_dialog_desc": "<PERSON><PERSON><PERSON> as informações necessárias abaixo.", "com_ui_mcp_enter_var": "Insira o valor para {{0}}", "com_ui_mcp_server_not_found": "Servidor não encontrado", "com_ui_mcp_servers": "Servidores MCP", "com_ui_mcp_url": "URL do servidor MCP", "com_ui_memories": "Me<PERSON><PERSON><PERSON><PERSON>", "com_ui_memories_allow_create": "Permitir <PERSON> de Memórias", "com_ui_memories_allow_opt_out": "Permitir que os usuários optem por não usar Memórias", "com_ui_memories_allow_read": "Permit<PERSON> Memórias", "com_ui_memories_allow_update": "Permitir atualização de Memórias", "com_ui_memories_allow_use": "<PERSON><PERSON><PERSON> Memória<PERSON>", "com_ui_memories_filter": "Filtrar memórias...", "com_ui_memory": "Memória", "com_ui_memory_created": "Memória criada com sucesso", "com_ui_memory_deleted": "Memória excluída", "com_ui_memory_deleted_items": "Memórias excluídas", "com_ui_memory_key_exists": "Uma memória com esta chave já existe. Por favor, use uma chave diferente.", "com_ui_memory_updated": "Memória atualizada com sucesso", "com_ui_memory_updated_items": "Memórias atualizadas", "com_ui_new_conversation_title": "Nova conversa", "com_ui_no_personalization_available": "Nenhuma personalização disponível no momento", "com_ui_no_read_access": "Você não tem permissão para visualizar memórias", "com_ui_oauth_connected_to": "Conectado a", "com_ui_oauth_error_callback_failed": "Falha na autenticação. Por favor, tente novamente.", "com_ui_oauth_error_generic": "Falha na autenticação. Por favor, tente novamente.", "com_ui_oauth_error_invalid_state": "Parâmetro de estado inválido. Por favor, tente novamente.", "com_ui_oauth_error_missing_code": "Código de autorização ausente. Por favor, tente novamente.", "com_ui_oauth_error_missing_state": "Parâmetro de estado ausente. Por favor, tente novamente.", "com_ui_oauth_error_title": "Falha na autenticação", "com_ui_oauth_success_description": "Sua autenticação foi bem-sucedida. Esta janela será fechada em", "com_ui_oauth_success_title": "Autenticação bem-sucedida", "com_ui_optional": "(opcional)", "com_ui_preferences_updated": "Preferências atualizadas com sucesso", "com_ui_quality": "Qualidade", "com_ui_redirecting_to_provider": "Redirecionando para {{0}}, por favor aguarde...", "com_ui_reference_saved_memories": "Referenciar me<PERSON>ó<PERSON> salvas", "com_ui_reference_saved_memories_description": "Permitir que o assistente referencie e use suas memórias salvas ao responder", "com_ui_rename_conversation": "Renomear conversa", "com_ui_rename_failed": "Falha ao renomear conversa", "com_ui_save_badge_changes": "Salvar alterações de insígnia?", "com_ui_saving": "Salvando...", "com_ui_seconds": "segundos", "com_ui_select_all": "Selecionar todos", "com_ui_show_image_details": "<PERSON><PERSON> de<PERSON><PERSON> da <PERSON>m", "com_ui_special_var_current_date": "Data atual", "com_ui_special_var_current_datetime": "Data e hora atual", "com_ui_special_var_current_user": "<PERSON><PERSON><PERSON><PERSON> atual", "com_ui_special_var_iso_datetime": "Data e hora UTC ISO", "com_ui_special_variables_more_info": "Você pode selecionar variáveis especiais do menu suspenso: `{{current_date}}` (data atual e dia da semana), `{{current_datetime}}` (data e hora local), `{{utc_iso_datetime}}` (data e hora UTC ISO) e `{{current_user}}` (nome de usuário).", "com_ui_sr_actions_menu": "<PERSON><PERSON> o menu de ações para \"{{0}}\"", "com_ui_temporary": "Conversa temporária", "com_ui_token": "token", "com_ui_tokens": "tokens", "com_ui_tool_collection_prefix": "Uma coleção de ferramentas de", "com_ui_tool_info": "Informações da ferramenta", "com_ui_tool_more_info": "Mais informações sobre esta ferramenta", "com_ui_trust_app": "Eu confio neste aplicativo", "com_ui_untitled": "<PERSON><PERSON> tí<PERSON>lo", "com_ui_update_mcp_error": "Houve um erro ao atualizar o MCP", "com_ui_update_mcp_success": "MCP atualizado com sucesso", "com_ui_usage": "<PERSON><PERSON>", "com_ui_use_memory": "Usar me<PERSON>ó<PERSON>", "com_ui_value": "Valor", "com_ui_view_memory": "Ver memória", "com_ui_web_search": "Pesquisa na web", "com_ui_web_search_api_subtitle": "Pesquisar na web por informações atualizadas", "com_ui_web_search_cohere_key": "Insira sua chave de API Cohere", "com_ui_web_search_firecrawl_url": "Insira a URL da API Firecrawl", "com_ui_web_search_jina_key": "Insira sua chave de API Jina", "com_ui_web_search_processing": "Processando resultados", "com_ui_web_search_provider": "<PERSON><PERSON><PERSON>es<PERSON>", "com_ui_web_search_provider_serper": "Serper API", "com_ui_web_search_provider_serper_key": "Insira sua chave de API Serper", "com_ui_web_search_reading": "<PERSON><PERSON> resultados", "com_ui_web_search_reranker": "<PERSON><PERSON><PERSON>", "com_ui_web_search_reranker_cohere": "Cohere", "com_ui_web_search_reranker_cohere_key": "Insira sua chave de API Cohere", "com_ui_web_search_reranker_jina": "Jina AI", "com_ui_web_search_reranker_jina_key": "Insira sua chave de API Jina", "com_ui_web_search_scraper": "<PERSON><PERSON><PERSON>", "com_ui_web_search_scraper_firecrawl": "Firecrawl API", "com_ui_web_search_scraper_firecrawl_key": "Insira sua chave de API Firecrawl", "com_ui_web_searching": "Pesquisando na web", "com_ui_web_searching_again": "Pesquisando na web novamente", "com_ui_weekend_morning": "Bom fim de semana", "com_ui_x_selected": "{{0}} se<PERSON><PERSON><PERSON>"}