{"chat_direction_left_to_right": "något måste fyllas i här, saknades", "chat_direction_right_to_left": "något måste fyllas i här. saknades", "com_a11y_ai_composing": "AI:n komponerar fortfarande.", "com_a11y_end": "AI har avslutat sitt svar.", "com_a11y_start": "AI har påb<PERSON><PERSON><PERSON><PERSON> sitt svar.", "com_agents_allow_editing": "<PERSON><PERSON><PERSON> andra anv<PERSON>re att redigera din agent", "com_agents_by_librechat": "av LibreChat", "com_agents_code_interpreter": "När den är aktiverad kan din agent utnyttja LibreChat API för kodtolkning för att köra genererad kod, inklusive filbehandling, på ett säkert sätt. Kräver en giltig API-nyckel.", "com_agents_code_interpreter_title": "API för kodtolkning", "com_agents_create_error": "Det uppstod ett fel vid skapandet av din agent.", "com_agents_description_placeholder": "Valfritt: Beskriv din agent här", "com_agents_enable_file_search": "Aktivera filsökning", "com_agents_file_context": "<PERSON><PERSON><PERSON>xt (OCR)", "com_agents_file_context_disabled": "Agent m<PERSON><PERSON> skapas innan filer laddas upp för fi<PERSON>.", "com_agents_file_context_info": "Filer som laddas upp som kontext bearbetas med OCR för att extrahera text, som sedan läggs till i agentens instruktioner. Lämpligt för dokument, bilder med text eller PDF-filer där du behöver hela textinnehållet i en fil", "com_agents_file_search_disabled": "Agenten måste skapas innan du laddar upp filer för <PERSON>", "com_agents_file_search_info": "<PERSON><PERSON>r detta är aktiverat kommer agenten se exakta filnamn som anges nedan för att hämta relevant information från dessa filer.", "com_agents_instructions_placeholder": "De systeminstruktioner som agenten använder", "com_agents_mcp_description_placeholder": "Förklara vad den gör med några få ord", "com_agents_mcp_icon_size": "Minsta storlek 128 x 128 px", "com_agents_mcp_info": "Lägg till MCP-servrar till din agent så att den kan utföra uppgifter och interagera med externa tjänster", "com_agents_mcp_name_placeholder": "Anpassat verktyg", "com_agents_mcp_trust_subtext": "Anpassade anslutningar verifieras inte av LibreChat", "com_agents_mcps_disabled": "Du måste skapa en agent innan du lägger till MCP:er.", "com_agents_missing_provider_model": "<PERSON><PERSON><PERSON>j lever<PERSON>ör och modell innan du skapar en agent.", "com_agents_name_placeholder": "Valfritt: <PERSON><PERSON> på <PERSON>en", "com_agents_no_access": "Du har inte tillgång till att redigera denna agent.", "com_agents_no_agent_id_error": "Inget agent-ID hittades. Se till att agenten skapas först.", "com_agents_not_available": "Agenten är ej till<PERSON>g", "com_agents_search_info": "När detta är aktiverat kan din agent söka på webben efter aktuell information. Kräver en giltig API-nyckel.", "com_agents_search_name": "<PERSON><PERSON><PERSON> efter agenter med namn", "com_agents_update_error": "Det uppstod ett fel vid uppdateringen av din agent.", "com_assistants_action_attempt": "Assistenten vill prata med {{0}}", "com_assistants_actions": "Åtgärder", "com_assistants_actions_disabled": "Du måste skapa en assistent innan du kan lägga till åtgärder.", "com_assistants_actions_info": "<PERSON><PERSON><PERSON> din assistent hämta information eller vidta åtgärder via API:er", "com_assistants_add_actions": "Lägg till åtgärder", "com_assistants_add_tools": "Lägg till verktyg", "com_assistants_allow_sites_you_trust": "<PERSON><PERSON><PERSON> bara webbplatser du litar på.", "com_assistants_append_date": "<PERSON><PERSON><PERSON> till aktuellt datum och tid", "com_assistants_append_date_tooltip": "<PERSON><PERSON>r detta är aktiverat kommer aktuellt datum och tid hos klienten läggas till i instruktionerna för hjälpsystemet.", "com_assistants_attempt_info": "Assistent vill ski<PERSON>a f<PERSON>:", "com_assistants_available_actions": "Tillgängliga åtgärder", "com_assistants_capabilities": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_assistants_code_interpreter": "<PERSON><PERSON>ol<PERSON><PERSON>", "com_assistants_code_interpreter_files": "Filerna nedan är endast avsedda för kod<PERSON>:", "com_assistants_code_interpreter_info": "En kodtolkare gör det möjligt för assistenten att skriva och köra kod. Verktyget kan bearbeta filer med olika data och formatering och generera filer som t.ex. grafer.", "com_assistants_completed_action": "Kommunicerade med {{0}}", "com_assistants_completed_function": "<PERSON><PERSON><PERSON> {{0}}", "com_assistants_conversation_starters": "Konversationsstartare", "com_assistants_conversation_starters_placeholder": "Lägg in en konversationsstartare", "com_assistants_create_error": "Det uppstod ett fel vid skapande av din assistent.", "com_assistants_create_success": "Skapad", "com_assistants_delete_actions_error": "Det uppstod ett fel vid borttagningen av funktionen.", "com_assistants_delete_actions_success": "Funktionen borttagen från assistenten", "com_assistants_description_placeholder": "Valfritt: <PERSON><PERSON><PERSON><PERSON> din assistent", "com_assistants_domain_info": "Assistenten skickade denna information till {{0}}", "com_assistants_file_search": "Filsökning", "com_assistants_file_search_info": "Filsökning gör det möjligt för assistenten att hämta kunskap från filer som du eller dina användare laddar upp. När en fil har laddats upp bestämmer assistenten automatiskt när den ska hämta innehåll baserat på användarens begäran. Det finns ännu inget stöd för att bifoga vektor-lager för filsökning. Du kan bifoga dem från Provider Playground eller bifoga filer till meddelanden för filsökning per tråd.", "com_assistants_function_use": "Assistent använde {{0}}", "com_assistants_instructions_placeholder": "Systeminstruktioner som assistent ska använda", "com_assistants_knowledge": "Kunskap", "com_assistants_max_starters_reached": "Max antal konversationsstartare uppnått", "com_assistants_name_placeholder": "Valfritt: Assistentens namn", "com_assistants_non_retrieval_model": "Filsökning är inte aktiverad på den här modellen. Vänligen välj en annan modell.", "com_assistants_retrieval": "<PERSON><PERSON><PERSON><PERSON>", "com_assistants_running_action": "<PERSON><PERSON><PERSON>", "com_assistants_running_var": "<PERSON><PERSON><PERSON> {{0}}", "com_assistants_search_name": "Sök assistenter efter namn", "com_assistants_update_actions_error": "Det uppstod ett fel vid skapandet eller uppdateringen av funktionen.", "com_assistants_update_actions_success": "<PERSON><PERSON><PERSON><PERSON><PERSON> skapades eller uppdaterades", "com_assistants_update_error": "Det uppstod ett fel vid uppdateringen av din assistent.", "com_assistants_update_success": "Uppdatering lyckades", "com_auth_already_have_account": "Har du redan ett konto?", "com_auth_apple_login": "Logga in med Apple", "com_auth_back_to_login": "Tillbaka till inloggning", "com_auth_click": "<PERSON><PERSON><PERSON>", "com_auth_click_here": "<PERSON><PERSON><PERSON> hä<PERSON>", "com_auth_continue": "Fortsätt", "com_auth_create_account": "<PERSON><PERSON><PERSON> ditt konto", "com_auth_discord_login": "Logga in med Discord", "com_auth_email": "E-post", "com_auth_email_address": "E-postadress", "com_auth_email_max_length": "E-post får inte vara längre än 120 tecken", "com_auth_email_min_length": "E-post måste vara minst 6 tecken", "com_auth_email_pattern": "Du måste ange en giltig e-postadress", "com_auth_email_required": "E-post krävs", "com_auth_email_resend_link": "Skicka e-post igen", "com_auth_email_resent_failed": "Missly<PERSON><PERSON> att skicka e-post med ny verifiering", "com_auth_email_resent_success": "E-post med verifiering skickat", "com_auth_email_verification_failed": "Verifiering via e-post misslyckades", "com_auth_email_verification_failed_token_missing": "Verifiering misslyckades, token saknas", "com_auth_email_verification_in_progress": "Verifierar din e-post, vänligen vänta", "com_auth_email_verification_invalid": "Ogiltig e-postverifiering", "com_auth_email_verification_redirecting": "Omdirigering om {{0}} sekunder...", "com_auth_email_verification_resend_prompt": "Fick du inte e-postmeddelandet?", "com_auth_email_verification_success": "E-post verifierad", "com_auth_email_verifying_ellipsis": "Verifierar...", "com_auth_error_create": "Det uppstod ett fel när du försökte registrera ditt konto. Vänligen försök igen.", "com_auth_error_invalid_reset_token": "<PERSON><PERSON> l<PERSON>återställningsnyckel är inte längre giltigt.", "com_auth_error_login": "Kunde inte logga in med den angivna informationen. Kontrollera dina uppgifter och försök igen.", "com_auth_error_login_rl": "F<PERSON>r många inloggningsförsök från den här IP-adressen på kort tid. Vänligen försök igen senare.", "com_auth_error_login_server": "Det uppstod ett internt serverfel. Vänligen vänta en stund och försök igen.", "com_auth_facebook_login": "Logga in med Facebook", "com_auth_full_name": "Fullständigt namn", "com_auth_github_login": "Logga in med Github", "com_auth_google_login": "Logga in med Google", "com_auth_here": "HÄR", "com_auth_login": "Logga in", "com_auth_login_with_new_password": "Du kan nu logga in med ditt nya lö<PERSON>.", "com_auth_name_max_length": "Namnet får inte vara längre än 80 tecken", "com_auth_name_min_length": "Namnet måste vara minst 3 tecken", "com_auth_name_required": "<PERSON>n krävs", "com_auth_no_account": "Har du inget konto?", "com_auth_password": "L<PERSON>senord", "com_auth_password_confirm": "Bekräfta lösenord", "com_auth_password_forgot": "Glömt lösenord?", "com_auth_password_max_length": "Lösenordet får inte vara längre än 128 tecken", "com_auth_password_min_length": "Lösenordet måste vara minst 8 tecken", "com_auth_password_not_match": "Lösenorden matchar inte", "com_auth_password_required": "Lösenord krävs", "com_auth_reset_password": "<PERSON><PERSON><PERSON><PERSON><PERSON> ditt l<PERSON>", "com_auth_reset_password_link_sent": "E-post skickad", "com_auth_reset_password_success": "Lösenordsåterställning lyckades", "com_auth_sign_in": "Logga in", "com_auth_sign_up": "Registrera dig", "com_auth_submit_registration": "<PERSON><PERSON><PERSON> registrering", "com_auth_to_reset_your_password": "f<PERSON>r att återställa ditt lösenord.", "com_auth_to_try_again": "för att försöka igen.", "com_auth_username": "Användarnamn (valfritt)", "com_auth_username_max_length": "Användarnamnet får inte vara längre än 20 tecken", "com_auth_username_min_length": "Användarnamnet måste vara minst 2 tecken", "com_auth_verify_your_identity": "Verifiera din identitet", "com_auth_welcome_back": "Välkommen tillbaka", "com_citation_more_details": "Mer information om {{label}}", "com_citation_source": "<PERSON><PERSON><PERSON>", "com_click_to_download": "(klicka här för att ladda ner)", "com_download_expired": "(nedladdningen har löpt ut)", "com_download_expires": "(k<PERSON>a här för att ladda ner - upphör att gälla {{0}})", "com_endpoint": "Slutpunkt", "com_endpoint_agent": "Agent", "com_endpoint_agent_model": "Agentmodell (Rekommenderad: GPT-3.5)", "com_endpoint_agent_placeholder": "Vänligen välj en agent", "com_endpoint_ai": "AI", "com_endpoint_anthropic_maxoutputtokens": "Maximalt antal tokens som kan genereras i svaret. Ange ett lägre värde för kortare svar och ett högre värde för längre svar.", "com_endpoint_anthropic_prompt_cache": "Prompt cachelagring gör det möjligt att återanvända stora sammanhang eller instruktioner mellan API-anrop, vilket minskar kostnaderna och fördröjningen", "com_endpoint_anthropic_temp": "Varierar mellan 0 och 1. Använd temp närmare 0 för analytiska/flervalsfrågor och närmare 1 för kreativa och generativa uppgifter. Vi rekommenderar att ändra detta eller Top P men inte båda.", "com_endpoint_anthropic_thinking": "Möjliggör interna resonemang fö<PERSON>-modeller som stöds (3.7 Sonnet). Obs: kräver att \"Thinking Budget\" är inställd och lägre än \"Max Output Tokens\"", "com_endpoint_anthropic_topk": "Top-k ändrar hur modellen väljer tokens för utdata. Ett top-k av 1 innebär att den valda token är den mest sannolika bland alla tokens i modellens vokabulär (kallas också girig avkodning), medan ett top-k av 3 innebär att nästa token väljs bland de 3 mest sannolika tokens (med temperatur).", "com_endpoint_anthropic_topp": "Top-p ändrar hur modellen väljer tokens för utdata. Tokens väljs från de mest K (se topK-parameter) sannolika till de minst tills summan av deras sannolikheter når top-p-värdet.", "com_endpoint_assistant": "Assistent", "com_endpoint_assistant_model": "Assistent-modell", "com_endpoint_assistant_placeholder": "Vä<PERSON>j en assistent från den högra sidopanelen", "com_endpoint_completion": "Komplettering", "com_endpoint_completion_model": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Rekommenderad: GPT-4)", "com_endpoint_config_click_here": "<PERSON><PERSON><PERSON> hä<PERSON>", "com_endpoint_config_key": "Ange API-nyckel", "com_endpoint_config_key_encryption": "<PERSON> n<PERSON>l kommer att krypteras och raderas vid", "com_endpoint_config_key_for": "Ange API-nyckel för", "com_endpoint_config_key_google_need_to": "<PERSON>", "com_endpoint_config_key_google_service_account": "Skapa ett tjänstekonto", "com_endpoint_config_key_google_vertex_ai": "Aktivera Vertex AI", "com_endpoint_config_key_google_vertex_api": "API på Google Cloud, sedan", "com_endpoint_config_key_google_vertex_api_role": "Se till att klicka på \"Skapa och fortsätt\" för att ge åtminstone rollen \"Vertex AI-användare\". Skapa slutligen en JSON-nyckel att importera här.", "com_endpoint_config_key_import_json_key": "Importera JSON-nyckel för k<PERSON>ot.", "com_endpoint_config_key_import_json_key_invalid": "Ogiltig JSON-n<PERSON><PERSON><PERSON> för konto, importerade du rätt fil?", "com_endpoint_config_key_import_json_key_success": "Lyckades importera JSON-nyckel för kontot", "com_endpoint_config_key_name": "<PERSON><PERSON><PERSON><PERSON>", "com_endpoint_config_key_never_expires": "<PERSON> n<PERSON>l kommer aldrig löpa ut", "com_endpoint_config_value": "Ange värde för", "com_endpoint_context": "Kontext", "com_endpoint_custom_name": "Anpassat namn", "com_endpoint_default": "standard", "com_endpoint_default_blank": "standard: tom", "com_endpoint_default_empty": "standard: tom", "com_endpoint_default_with_num": "standard: {{0}}", "com_endpoint_examples": " Förinställningar", "com_endpoint_export": "Exportera", "com_endpoint_export_share": "Exportera/dela", "com_endpoint_frequency_penalty": "Frekvensstraff", "com_endpoint_func_hover": "Aktivera användning av tillägg som OpenAI-funktioner", "com_endpoint_google_custom_name_placeholder": "Ange ett anpassat namn för Google", "com_endpoint_google_maxoutputtokens": "Maximalt antal tokens som kan genereras i svaret. Ange ett lägre värde för kortare svar och ett högre värde för längre svar.", "com_endpoint_google_temp": "Högre värden = mer slump<PERSON>, medan lägre värden = mer fokuserat och bestämt. Vi rekommenderar att ändra detta eller Top P men inte båda.", "com_endpoint_google_topk": "Top-k ändrar hur modellen väljer tokens för utdata. Ett top-k av 1 innebär att den valda token är den mest sannolika bland alla tokens i modellens vokabulär (kallas också girig avkodning), medan ett top-k av 3 innebär att nästa token väljs bland de 3 mest sannolika tokens (med temperatur).", "com_endpoint_google_topp": "Top-p ändrar hur modellen väljer tokens för utdata. Tokens väljs från de mest K (se topK-parameter) sannolika till de minst tills summan av deras sannolikheter når top-p-värdet.", "com_endpoint_max_output_tokens": "<PERSON> ut<PERSON>", "com_endpoint_message": "Meddelande", "com_endpoint_message_new": "Meddelande {{0}}", "com_endpoint_message_not_appendable": "<PERSON><PERSON><PERSON> ditt meddelande eller <PERSON>.", "com_endpoint_my_preset": "<PERSON>", "com_endpoint_no_presets": "Ingen förinställning ännu", "com_endpoint_open_menu": "Öppna meny", "com_endpoint_openai_custom_name_placeholder": "Ange ett eget namn för ChatGPT", "com_endpoint_openai_freq": "<PERSON><PERSON><PERSON> mellan -2,0 och 2,0. Positiva värden minskar nya tokens baserat på deras befintliga frekvens i texten hittills, vilket minskar modellens sannolikhet att upprepa samma rad ordagrant.", "com_endpoint_openai_max": "Max tokens att generera. Den totala längden på tokens för inmatning och svar är begränsad av modellen som används.", "com_endpoint_openai_pres": "<PERSON><PERSON>mer mellan -2,0 och 2,0. Positiva värden minskar nya tokens baserat på om de förekommer i texten hittills, vilket ökar modellens sannolikhet att prata om nya ämnen.", "com_endpoint_openai_prompt_prefix_placeholder": "Ange anpassade instruktioner att inkludera i Systemmeddelande. Standard: inga", "com_endpoint_openai_temp": "Högre värden = mer slump<PERSON>, medan lägre värden = mer fokuserat och bestämt. Vi rekommenderar att ändra detta eller Top P men inte båda.", "com_endpoint_openai_topp": "Ett alternativ till temperatur, kallat k<PERSON>, där modellen beaktar resultaten av tokens med top_p-sannolikhetsmassa. Så 0,1 innebär att endast de tokens som utgör den översta 10% sannolikhetsmassan beaktas. Vi rekommenderar att ändra detta eller temperaturen men inte båda.", "com_endpoint_output": "Utdata", "com_endpoint_plug_image_detail": "Bildd<PERSON><PERSON>", "com_endpoint_plug_resend_files": "<PERSON><PERSON><PERSON> filer på nytt", "com_endpoint_plug_set_custom_instructions_for_gpt_placeholder": "Ange anpassade instruktioner att inkludera i systemmeddelande. Standard: inga", "com_endpoint_plug_skip_completion": "<PERSON><PERSON> över komplettering", "com_endpoint_plug_use_functions": "<PERSON><PERSON><PERSON><PERSON>", "com_endpoint_presence_penalty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_endpoint_preset": "förinställning", "com_endpoint_preset_custom_name_placeholder": "något måste fyllas i här. saknades", "com_endpoint_preset_default": "är nu standardförinställningen.", "com_endpoint_preset_default_item": "Standard:", "com_endpoint_preset_default_none": "Ingen standardförinställning aktiv.", "com_endpoint_preset_name": "Förinställningsnamn", "com_endpoint_presets": "förinställningar", "com_endpoint_presets_clear_warning": "Är du säker på att du vill rensa alla förinställningar? Detta går inte att ångra.", "com_endpoint_prompt_cache": "Anv<PERSON>nd Prompt-cachning", "com_endpoint_prompt_prefix": "Uppmaningsprefix", "com_endpoint_prompt_prefix_assistants": "Ytterligare instruktioner", "com_endpoint_prompt_prefix_placeholder": "Ange anpassade instruktioner eller kontext. Ignoreras om tom.", "com_endpoint_save_as_preset": "Spara som förinställning", "com_endpoint_search_endpoint_models": "<PERSON><PERSON><PERSON> {{0}} modeller...", "com_endpoint_search_models": "<PERSON><PERSON><PERSON> modeller...", "com_endpoint_search_var": "<PERSON><PERSON><PERSON> {{0}}...", "com_endpoint_set_custom_name": "<PERSON>e ett eget namn, om du kan hitta denna förin<PERSON>ä<PERSON>ning", "com_endpoint_skip_hover": "Aktivera att hoppa över kompletteringssteg, som granskar det slutliga svaret och genererade steg", "com_endpoint_stop": "Stoppsekvenser", "com_endpoint_stop_placeholder": "Separera värdena genom att trycka på `Enter`.", "com_endpoint_temperature": "Temperatur", "com_endpoint_thinking": "Tänkande", "com_endpoint_top_k": "Top K", "com_endpoint_top_p": "Top P", "com_nav_archive_created_at": "Skapad", "com_nav_archive_name": "<PERSON><PERSON>", "com_nav_archived_chats": "Arkiverade chattar", "com_nav_balance": "Balans", "com_nav_clear_all_chats": "<PERSON>sa alla chattar", "com_nav_clear_conversation": "<PERSON><PERSON> konvers<PERSON>", "com_nav_clear_conversation_confirm_message": "Är du säker på att du vill rensa alla konversationer? Detta går inte att ångra.", "com_nav_close_sidebar": "<PERSON><PERSON><PERSON> sido<PERSON>lt", "com_nav_confirm_clear": "Bekräfta rensning", "com_nav_enabled": "Aktiverad", "com_nav_export": "Exportera", "com_nav_export_all_message_branches": "Exportera alla grenar för <PERSON>", "com_nav_export_conversation": "Exportera konversation", "com_nav_export_filename": "Filnamn", "com_nav_export_filename_placeholder": "<PERSON><PERSON>", "com_nav_export_include_endpoint_options": "Inkludera slutpunktsalternativ", "com_nav_export_recursive": "Rekursiv", "com_nav_export_recursive_or_sequential": "Re<PERSON><PERSON><PERSON> eller sekventiell?", "com_nav_export_type": "<PERSON><PERSON>", "com_nav_font_size": "Textstorlek", "com_nav_help_faq": "Hjälp & Vanliga frågor", "com_nav_lang_arabic": "العربية", "com_nav_lang_auto": "Upptäck automatiskt", "com_nav_lang_brazilian_portuguese": "Portuguê<PERSON>", "com_nav_lang_chinese": "中文", "com_nav_lang_dutch": "Nederlands", "com_nav_lang_english": "English", "com_nav_lang_estonian": "<PERSON><PERSON><PERSON> keel", "com_nav_lang_finnish": "<PERSON><PERSON>", "com_nav_lang_french": "Français ", "com_nav_lang_georgian": "ქართული", "com_nav_lang_german": "De<PERSON>ch", "com_nav_lang_hebrew": "עברית", "com_nav_lang_indonesia": "Indonesia", "com_nav_lang_italian": "Italiano", "com_nav_lang_japanese": "日本語", "com_nav_lang_korean": "한국어", "com_nav_lang_polish": "<PERSON><PERSON>", "com_nav_lang_portuguese": "Português", "com_nav_lang_russian": "Русский", "com_nav_lang_spanish": "Español", "com_nav_lang_swedish": "Svenska", "com_nav_lang_thai": "ไทย", "com_nav_lang_traditional_chinese": "繁體中文", "com_nav_lang_turkish": "Türkçe", "com_nav_lang_vietnamese": "Tiếng <PERSON>", "com_nav_language": "Språk", "com_nav_log_out": "Logga ut", "com_nav_not_supported": "<PERSON><PERSON><PERSON> ej", "com_nav_open_sidebar": "<PERSON><PERSON><PERSON> sido<PERSON>", "com_nav_plugin_auth_error": "Det uppstod ett fel när försöket att autentisera denna plugin gjordes. Försök igen.", "com_nav_plugin_search": "Sök efter plugins", "com_nav_plugin_store": "Pluginbutik", "com_nav_search_placeholder": "<PERSON><PERSON><PERSON>", "com_nav_send_message": "<PERSON><PERSON><PERSON> medd<PERSON>", "com_nav_setting_data": "Datakontroller", "com_nav_setting_general": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_settings": "Inställningar", "com_nav_shared_links": "Delade länkar", "com_nav_theme": "<PERSON><PERSON>", "com_nav_theme_dark": "M<PERSON><PERSON><PERSON>", "com_nav_theme_light": "<PERSON><PERSON><PERSON>", "com_nav_theme_system": "System", "com_nav_user": "ANVÄNDARE", "com_ui_accept": "<PERSON><PERSON> accepterar", "com_ui_agents": "<PERSON><PERSON>", "com_ui_agents_allow_create": "<PERSON><PERSON><PERSON> att skapa agenter", "com_ui_agents_allow_use": "<PERSON><PERSON>t användning av agenter", "com_ui_all": "alla", "com_ui_archive": "Arkiv", "com_ui_archive_error": "Kunde inte arkivera chatt", "com_ui_bookmark_delete_confirm": "Är du säker på att du vill ta bort detta bokmärke?", "com_ui_bookmarks": "Bokmärken", "com_ui_bookmarks_add_to_conversation": "Lägg till i nuvarande konversation", "com_ui_bookmarks_count": "<PERSON><PERSON>", "com_ui_bookmarks_create_error": "Ett fel uppstod vid skapandet av bokmärket", "com_ui_bookmarks_create_success": "Bokmärke skapat framgångsrikt", "com_ui_bookmarks_delete_error": "Ett fel uppstod vid raderingen av bokmärket", "com_ui_bookmarks_delete_success": "Bokm��rke raderat framgångsrikt", "com_ui_bookmarks_description": "Beskrivning", "com_ui_bookmarks_new": "Nytt Bokmärke", "com_ui_bookmarks_title": "Titel", "com_ui_bookmarks_update_error": "Ett fel uppstod vid uppdateringen av bokmärket", "com_ui_bookmarks_update_success": "Bokmärke uppdaterat framgångsrikt", "com_ui_cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_clear": "Ren<PERSON>", "com_ui_close": "Stäng", "com_ui_confirm_action": "Bekräfta åtgärd", "com_ui_continue": "Fortsätt", "com_ui_copied_to_clipboard": "Kopierat till urklipp", "com_ui_copy_link": "<PERSON><PERSON><PERSON> länk", "com_ui_copy_to_clipboard": "Kopiera till urklipp", "com_ui_create_link": "Skapa länk", "com_ui_decline": "Jag accepterar inte", "com_ui_delete": "<PERSON><PERSON><PERSON>", "com_ui_delete_confirm": "<PERSON><PERSON> kommer att radera", "com_ui_delete_conversation": "<PERSON><PERSON><PERSON> chatt?", "com_ui_edit": "Rediger<PERSON>", "com_ui_enter": "<PERSON><PERSON>", "com_ui_examples": "Exempel", "com_ui_happy_birthday": "Det är min första födelsedag!", "com_ui_import_conversation_error": "Det uppstod ett fel vid import av dina konversationer", "com_ui_import_conversation_info": "Importera konversationer från en JSON-fil", "com_ui_import_conversation_success": "Konversationer har importerats framgångsrikt", "com_ui_input": "Inmatning", "com_ui_model": "<PERSON><PERSON>", "com_ui_new_chat": "<PERSON><PERSON> chatt", "com_ui_next": "<PERSON><PERSON><PERSON>", "com_ui_no_terms_content": "Ingen innehåll för villkor för användning att visa", "com_ui_of": "av", "com_ui_prev": "Föregående", "com_ui_regenerate": "Återskapa", "com_ui_rename": "byta namn på", "com_ui_revoke": "Å<PERSON><PERSON><PERSON>", "com_ui_revoke_info": "Återkalla alla användaruppgifter.", "com_ui_save": "Spara", "com_ui_select_model": "<PERSON><PERSON><PERSON><PERSON> en modell", "com_ui_share": "Dela", "com_ui_share_create_message": "Ditt namn och alla meddelanden du lägger till efter delningen förblir privata.", "com_ui_share_delete_error": "Ett fel uppstod vid borttagningen av den delade länken.", "com_ui_share_error": "Ett fel uppstod vid delning av chattlänken", "com_ui_share_link_to_chat": "Dela länk till chatt", "com_ui_share_update_message": "<PERSON><PERSON> namn, anpassade instruktioner och alla meddelanden du lägger till efter delningen förblir privata.", "com_ui_shared_link_not_found": "Delad länk hittades inte", "com_ui_submit": "<PERSON><PERSON><PERSON>", "com_ui_terms_and_conditions": "Villkor för an<PERSON>", "com_ui_unarchive": "Avarkivera", "com_ui_unarchive_error": "Kunde inte avarkivera chatt", "com_ui_upload_success": "Uppladdningen av filen lyckades", "com_ui_use_prompt": "<PERSON><PERSON><PERSON><PERSON> prompt"}