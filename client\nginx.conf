server {
    listen 80;
    server_name dev.kintu.com.br;
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name dev.kintu.com.br;

    ssl_certificate /etc/letsencrypt/live/dev.kintu.com.br/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/dev.kintu.com.br/privkey.pem;

    location /api {
        proxy_pass http://api:3080/api;  # sua aplicação backend
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location / {
        proxy_pass http://api:3080;  # sua aplicação backend
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}