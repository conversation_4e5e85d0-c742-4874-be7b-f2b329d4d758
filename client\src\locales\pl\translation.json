{"chat_direction_left_to_right": "coś tu musi by<PERSON>. było pusto", "chat_direction_right_to_left": "coś tu musi by<PERSON>. było pusto", "com_a11y_ai_composing": "AI nadal komponuje.", "com_a11y_end": "AI zakończył swoją odpowiedź.", "com_a11y_start": "AI rozpoczął swoją odpowiedź.", "com_agents_allow_editing": "Zezwól by inni użytkownicy mogli edytować twojego agenta", "com_agents_by_librechat": "od LibreChat", "com_agents_code_interpreter_title": "API interpretera kodu", "com_agents_create_error": "Wystąpił błąd podczas tworzenia agenta.", "com_agents_description_placeholder": "Opcjonalnie: Opisz swojego agenta tutaj", "com_agents_enable_file_search": "Włącz wyszukiwanie plików", "com_agents_file_context": "Kontest Pliku (OCR)", "com_agents_file_context_disabled": "Agent musi zostać utworzony przed przesłaniem plików dla Kontekstu Plików", "com_agents_file_context_info": "Pliki przesłane jako \"Kontekst\" są przetworzone przez OCR by wyd<PERSON><PERSON><PERSON> tekst, kt<PERSON><PERSON> potem jest dodany do instrukcji Agenta. Jest to idealne dla dokumentów, obrazów z tekstem oraz plików PDF, gdzie potrzebujesz całego tekstu z pliku.", "com_agents_file_search_disabled": "Agent musi zostać utworzony przed przesłaniem plików do wyszukiwania.", "com_agents_file_search_info": "Po włączeniu agent zostanie poinformowany o dokładnych nazwach plików wymienionych poniżej, co pozwoli mu na pobranie odpowiedniego kontekstu z tych plików.", "com_agents_instructions_placeholder": "Instrukcje systemowe używane przez agenta", "com_agents_mcp_icon_size": "Minimalny rozmiar 128 x 128 px", "com_agents_missing_provider_model": "<PERSON><PERSON><PERSON><PERSON>w<PERSON>ę i model przed utworzeniem agenta.", "com_agents_name_placeholder": "Opcjonalnie: Nazwa agenta", "com_agents_no_access": "Nie masz zezwolenia na edycję tego agenta.", "com_agents_not_available": "Agent nie jest dos<PERSON><PERSON><PERSON><PERSON>", "com_agents_search_name": "Wyszukaj agentów po nazwie", "com_agents_update_error": "Wystąpił błąd podczas aktualizacji agenta.", "com_assistants_action_attempt": "<PERSON><PERSON><PERSON> ch<PERSON> r<PERSON> z {{0}}", "com_assistants_actions": "<PERSON><PERSON><PERSON><PERSON>", "com_assistants_actions_disabled": "Musisz utwo<PERSON><PERSON>ć asystenta przed dodaniem akcji.", "com_assistants_actions_info": "Pozwól swojemu Asystentowi pobierać informacje lub podejmować działania poprzez API", "com_assistants_add_actions": "<PERSON><PERSON><PERSON>", "com_assistants_add_tools": "Dodaj narzęd<PERSON>", "com_assistants_allow_sites_you_trust": "Zezwól tylko na strony, którym ufasz.", "com_assistants_append_date": "Dodaj aktualną datę i czas", "com_assistants_append_date_tooltip": "Po włączeniu, aktualna data i czas klienta zostaną dodane do instrukcji systemowych asystenta.", "com_assistants_attempt_info": "Asystent chce wysłać następującą treść: ", "com_assistants_available_actions": "Dostępne akcje", "com_assistants_capabilities": "<PERSON><PERSON><PERSON><PERSON>ści", "com_assistants_code_interpreter": "Interpreter kodu", "com_assistants_code_interpreter_files": "Poniższe pliki są tylko dla interpretera kodu:", "com_assistants_code_interpreter_info": "Interpreter kodu umożliwia asystentowi pisanie i uruchamianie kodu. To narzędzie może przetwarzać pliki z różnymi danymi i formatowaniem oraz generować pliki, takie jak wykresy.", "com_assistants_completed_action": "Rozmawiał z {{0}}", "com_assistants_completed_function": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{0}}", "com_assistants_conversation_starters": "Rozpoczęcie rozmowy", "com_assistants_conversation_starters_placeholder": "Wprowadź rozpoczęcie rozmowy", "com_assistants_create_error": "Wystą<PERSON>ł błąd podczas tworzenia asystenta.", "com_assistants_create_success": "Pomyślnie utworzono", "com_assistants_delete_actions_error": "<PERSON><PERSON><PERSON><PERSON><PERSON>ł błąd podczas usuwania akcji.", "com_assistants_delete_actions_success": "Pomyślnie usunięto akcję z asystenta", "com_assistants_description_placeholder": "Opcjonalnie: Opisz swojego asystenta tutaj", "com_assistants_domain_info": "Asystent wysłał te informacje do {{0}}", "com_assistants_file_search": "Wyszukiwanie plików", "com_assistants_file_search_info": "Wyszukiwanie plików umożliwia asystentowi dostęp do wiedzy z plików przesłanych przez ciebie lub twoich użytkowników. Po przesłaniu pliku asystent automatycznie decyduje, kiedy pobierać treść na podstawie żądań użytkownika. Dołączanie magazynów wektorowych do wyszukiwania plików nie jest jeszcze obsługiwane. Możesz je dołączyć z Playground dostawcy lub dołączyć pliki do wiadomości w celu wyszukiwania plików na podstawie wątku.", "com_assistants_function_use": "Asystent użył {{0}}", "com_assistants_image_vision": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "com_assistants_instructions_placeholder": "Instrukcje systemowe używane przez asystenta", "com_assistants_knowledge": "<PERSON><PERSON><PERSON>", "com_assistants_knowledge_disabled": "Asystent musi zostać utworzony, a Interpreter kodu lub Pobieranie musi być włączone i zapisane przed przesłaniem plików jako Wiedza.", "com_assistants_knowledge_info": "Jeśli prześlesz pliki w sek<PERSON><PERSON> Wiedza, rozmowy z twoim Asystentem mogą zawierać treść plików.", "com_assistants_max_starters_reached": "Osiągni<PERSON><PERSON> ma<PERSON>ą liczbę rozpoczęć rozmowy", "com_assistants_name_placeholder": "Opcjonalnie: Nazwa asystenta", "com_assistants_non_retrieval_model": "Wyszukiwanie w plikach nie jest włączone dla tego modelu. Proszę wybierz inny model.", "com_assistants_retrieval": "Pobieranie", "com_assistants_running_action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_assistants_search_name": "Wyszukaj asystentów po nazwie", "com_assistants_update_actions_error": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> błąd podczas tworzenia lub aktualizacji akcji.", "com_assistants_update_actions_success": "Pomyślnie utworzono lub zaktualizowano akcję", "com_assistants_update_error": "Wystąpił błąd podczas aktualizacji asystenta.", "com_assistants_update_success": "Pomyślnie zaktualizowano", "com_auth_already_have_account": "Masz już konto?", "com_auth_apple_login": "Zaloguj się przez Apple", "com_auth_back_to_login": "Powrót do logowania", "com_auth_click": "<PERSON><PERSON><PERSON><PERSON>", "com_auth_click_here": "<PERSON><PERSON><PERSON><PERSON> tutaj", "com_auth_continue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_auth_create_account": "Utwórz konto", "com_auth_discord_login": "Zaloguj się przez Discorda", "com_auth_email": "Email", "com_auth_email_address": "Adres e-mail", "com_auth_email_max_length": "Adres email nie może być dłuższy niż 120 znaków.", "com_auth_email_min_length": "Adres email musi mieć co najmniej 6 znaków.", "com_auth_email_pattern": "Wprowadź poprawny adres e-mail", "com_auth_email_required": "W<PERSON><PERSON>e jest podanie adresu email.", "com_auth_email_resend_link": "<PERSON><PERSON><PERSON><PERSON><PERSON> ponown<PERSON> email", "com_auth_email_resent_failed": "<PERSON>e udało się ponownie wysłać emaila weryfikacyjnego", "com_auth_email_resent_success": "<PERSON><PERSON>jny wysłany ponownie", "com_auth_email_verification_failed": "Weryfikacja email nie powiodła się", "com_auth_email_verification_failed_token_missing": "Weryfikacja nie powiodła się, brak <PERSON>u", "com_auth_email_verification_in_progress": "Weryfika<PERSON>ja twojego emaila, <PERSON><PERSON><PERSON>", "com_auth_email_verification_invalid": "Nieprawidłowa weryfikacja email", "com_auth_email_verification_redirecting": "Przekierowanie za {{0}} sekund...", "com_auth_email_verification_resend_prompt": "Nie otrzymałeś maila?", "com_auth_email_verification_success": "<PERSON><PERSON>wany pomyślnie", "com_auth_email_verifying_ellipsis": "Weryfikowanie...", "com_auth_error_create": "Wystąpił błąd podczas tworzenia konta. Spróbuj ponownie.", "com_auth_error_invalid_reset_token": "Ten token do resetowania hasła jest już nieważ<PERSON>.", "com_auth_error_login": "Nie udało się zalogować przy użyciu podanych danych. Sprawdź swoje dane logowania i spróbuj ponownie.", "com_auth_error_login_ban": "Twoje konto zostało tymczasowo zablokowane z powodu naruszeń reguł korzystania z naszego serwisu.", "com_auth_error_login_rl": "Zbyt wiele prób logowania w krótkim czasie. Spróbuj ponownie później.", "com_auth_error_login_server": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wewnętrzny błąd serwera,. <PERSON><PERSON><PERSON>, poczekaj chwilę i spróbuj ponownie.", "com_auth_error_login_unverified": "Twoje konto nie jest zweryfikowane. Sprawdź swoją skrzynkę email by znaleźć link weryfikacyjny.", "com_auth_facebook_login": "Zaloguj się przez Facebooka", "com_auth_full_name": "Pełne imię", "com_auth_github_login": "Zaloguj się przez Githuba", "com_auth_google_login": "Zaloguj się przez Google", "com_auth_here": "TUTAJ", "com_auth_login": "<PERSON><PERSON><PERSON><PERSON>", "com_auth_login_with_new_password": "Teraz możesz <PERSON>, używając nowego hasła.", "com_auth_name_max_length": "<PERSON><PERSON>ę nie może zawierać więcej niż 80 znaków", "com_auth_name_min_length": "<PERSON><PERSON>ę musi zawierać co najmniej 3 znaki", "com_auth_name_required": "<PERSON><PERSON><PERSON> jest wymagane", "com_auth_no_account": "Nie masz konta?", "com_auth_password": "<PERSON><PERSON><PERSON>", "com_auth_password_confirm": "Potwierd<PERSON> hasło", "com_auth_password_forgot": "Zapom<PERSON>ł<PERSON><PERSON> hasła?", "com_auth_password_max_length": "Hasło musi mieć mniej niż 128 znaków", "com_auth_password_min_length": "Hasło musi mieć co najmniej 8 znaków", "com_auth_password_not_match": "Hasła nie są zgodne", "com_auth_password_required": "<PERSON><PERSON><PERSON><PERSON> jest podanie hasła", "com_auth_registration_success_generic": "Sprawdź swoją skrzynkę email, aby zweryfikować adres email.", "com_auth_registration_success_insecure": "Rejestracja zakończona pomyślnie.", "com_auth_reset_password": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_auth_reset_password_if_email_exists": "<PERSON><PERSON><PERSON> konto z tym adresem e-mail istnieje, wiadomość e-mail z instrukcjami resetowania hasła została wysłana. Sprawdź folder spamu.", "com_auth_reset_password_link_sent": "Link do resetowania hasła został wysłany", "com_auth_reset_password_success": "Hasło zostało pomyślnie zresetowane", "com_auth_sign_in": "<PERSON><PERSON><PERSON><PERSON>", "com_auth_sign_up": "Zarejestruj się", "com_auth_submit_registration": "Zarejestruj się", "com_auth_to_reset_your_password": "aby z<PERSON><PERSON><PERSON><PERSON> hasło.", "com_auth_to_try_again": "aby s<PERSON><PERSON><PERSON><PERSON><PERSON> ponownie.", "com_auth_two_factor": "Sprawdź preferowaną aplikację do kodów 2FA, aby uzyskać kod.", "com_auth_username": "Nazwa użytkownika (opcjonalnie)", "com_auth_username_max_length": "Nazwa użytkownika nie może zawierać więcej niż 20 znaków", "com_auth_username_min_length": "Nazwa użytkownika musi zawierać co najmniej 2 znaki", "com_auth_verify_your_identity": "Zweryfikuj swoją to<PERSON><PERSON>", "com_auth_welcome_back": "Witamy z powrotem", "com_click_to_download": "(k<PERSON><PERSON><PERSON> tutaj, aby p<PERSON>)", "com_download_expired": "(pob<PERSON>nie wygasło)", "com_download_expires": "(k<PERSON><PERSON><PERSON> tutaj, aby p<PERSON> - w<PERSON><PERSON><PERSON> {{0}})", "com_endpoint": "Punkt końcowy", "com_endpoint_agent": "Agent", "com_endpoint_agent_model": "Model agenta (zalecany: GPT-3.5)", "com_endpoint_agent_placeholder": "Proszę wy<PERSON> agenta", "com_endpoint_ai": "AI", "com_endpoint_anthropic_maxoutputtokens": "Maksymalna liczba tokenów, która może zostać wygenerowana w odpowiedzi. Wybierz mniejszą wartość dla krótszych odpowiedzi i większą wartość dla dłuższych odpowiedzi.", "com_endpoint_anthropic_prompt_cache": "Prompt caching umożliwia ponowne wykorzystanie dużego kontekstu lub instrukcji w wywołaniach API, zmniejszając koszty i opóźnienia.", "com_endpoint_anthropic_temp": "Zakres od 0 do 1. Użyj wartości bliżej 0 dla analizy/wyboru wielokrotnego, a bliżej 1 dla zadań twórczych i generatywnych. Zalecamy dostosowanie tej wartości lub Top P, ale nie obu jednocześnie.", "com_endpoint_anthropic_thinking": "Włącza wewnętrzne rozumowanie dla wspieranych modeli Claude (3.7 Sonnet). Notatka: wymaga \"Thinking Budget\" by <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> oraz mniejszy niż \"Max Output Tokens\".", "com_endpoint_anthropic_topk": "Top-K wpływa na sposób wyboru tokenów przez model. Top-K równa 1 oznacza, że wybrany token jest najbardziej prawdopodobny spośród wszystkich tokenów w słowniku modelu (tzw. dekodowanie zachłanne), podczas gdy top-K równa 3 oznacza, że następny token zostaje wybrany spośród 3 najbardziej prawdopodobnych tokenów (za pomocą temperatury).", "com_endpoint_anthropic_topp": "Top-P wpływa na sposób wyboru tokenów przez model. Tokeny wybierane są od najbardziej prawdopodobnych do najmniej prawdopodobnych, aż suma ich prawdopodobieństw osiągnie wartość top-P.", "com_endpoint_assistant": "Asystent", "com_endpoint_assistant_model": "Model asystenta", "com_endpoint_assistant_placeholder": "Proszę wybrać asystenta z prawego panelu bocznego", "com_endpoint_completion": "Uzupełnienie", "com_endpoint_completion_model": "Model uzupełnienia (zalecany: GPT-4)", "com_endpoint_config_click_here": "<PERSON><PERSON><PERSON><PERSON> tutaj", "com_endpoint_config_google_api_info": "<PERSON><PERSON>zy<PERSON>ć klucz API języka generatywnego (dla Gemini),", "com_endpoint_config_google_api_key": "Klucz API Google", "com_endpoint_config_google_cloud_platform": "(z Google Cloud Platform)", "com_endpoint_config_google_gemini_api": "(API Gemini)", "com_endpoint_config_google_service_key": "Klucz konta usługi Google", "com_endpoint_config_key": "Ustaw klucz API", "com_endpoint_config_key_encryption": "Twój klucz zostanie zaszyfrowany i usunięty o", "com_endpoint_config_key_for": "Ustaw klucz API dla", "com_endpoint_config_key_google_need_to": "Powinieneś ", "com_endpoint_config_key_google_vertex_ai": "Włącz Vertex AI", "com_endpoint_config_key_import_json_key": "Importuj klucz JSON konta usługi.", "com_endpoint_config_key_import_json_key_invalid": "Nieprawidłowy klucz JSON konta usługi. <PERSON>zy zaimport<PERSON> właściwy plik?", "com_endpoint_config_key_import_json_key_success": "Pomyślnie zaimportowano klucz JSON konta usługi", "com_endpoint_config_key_name": "<PERSON><PERSON>cz", "com_endpoint_config_key_never_expires": "Twój klucz nigdy nie wygaśnie", "com_endpoint_config_placeholder": "Ustaw swój klucz w menu nagłówka, aby c<PERSON><PERSON>.", "com_endpoint_config_value": "<PERSON><PERSON><PERSON><PERSON><PERSON> wartość dla", "com_endpoint_context": "Kontekst", "com_endpoint_context_tokens": "Maksymalna liczba tokenów kontekstu", "com_endpoint_custom_name": "Niestandardowa nazwa", "com_endpoint_default": "domyślnie", "com_endpoint_default_blank": "domyślnie: puste", "com_endpoint_default_empty": "domyślnie: puste", "com_endpoint_default_with_num": "<PERSON><PERSON><PERSON><PERSON>ie: {{0}}", "com_endpoint_examples": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_endpoint_export": "Eksportuj", "com_endpoint_export_share": "Eksportuj/Udostępnij", "com_endpoint_frequency_penalty": "Kara za częstotli<PERSON>ść", "com_endpoint_func_hover": "Aktywuj wtyczki jako funkcje OpenAI", "com_endpoint_google_custom_name_placeholder": "Ustaw niestandardową nazwę dla Google", "com_endpoint_google_maxoutputtokens": "Maksymalna liczba tokenów, które mogą być wygenerowane w odpowiedzi. Wybierz niż<PERSON> wartość dla krótszych odpowiedzi i wyższą wartość dla dłuższych odpowiedzi.", "com_endpoint_google_temp": "Wyższe wartości oznaczają wi<PERSON><PERSON><PERSON><PERSON>, natomiast niższe wartości prowadzą do bardziej skoncentrowanych i deterministycznych wyników. Zalecamy dostosowanie tej wartości lub Top P, ale nie obu jednocześnie.", "com_endpoint_google_topk": "Top-k wpływa na sposób, w jaki model wybiera tokeny do wygenerowania odpowiedzi. Top-k 1 oznacza, że wybrany token jest najbardziej prawdopodobny spośród wszystkich tokenów w słowniku modelu (nazywane też dekodowaniem zachłannym), podczas gdy top-k 3 oznacza, że następny token jest wybierany spośród 3 najbardziej prawdopodobnych tokenów (z uwzględnieniem temperatury).", "com_endpoint_google_topp": "Top-p wp<PERSON><PERSON><PERSON> na sposób, w jaki model wybiera tokeny do wygenerowania odpowiedzi. Tokeny są wybierane od najbardziej prawdopodobnych do najmniej, aż suma ich prawdopodobieństw osiągnie wartość top-p.", "com_endpoint_instructions_assistants": "Nadpisz instrukcje", "com_endpoint_max_output_tokens": "Maksymalna liczba tokenów wyjściowych", "com_endpoint_message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_endpoint_message_new": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{0}}", "com_endpoint_message_not_appendable": "Edyt<PERSON>j swoją wiadomość lub wygeneruj ponownie.", "com_endpoint_my_preset": "<PERSON><PERSON> pre<PERSON> us<PERSON>wi<PERSON>", "com_endpoint_no_presets": "Brak zapisanych predefiniowanych ustawień", "com_endpoint_open_menu": "Otwórz menu", "com_endpoint_openai_custom_name_placeholder": "Ustaw własną nazwę dla ChatGPT", "com_endpoint_openai_freq": "Liczba pomiędzy -2,0 a 2,0. Dodatnie wartości karzą nowe tokeny w oparciu o ich dotychczasową częstotliwość występowania w tekście, co zmniejsza tendencję modelu do powtarzania tej samej linii dosłownie.", "com_endpoint_openai_max": "Maksymalna liczba tokenów do wygenerowania. Łączna długość tokenów wejściowych i wygenerowanych tokenów jest ograniczona długością kontekstu modelu.", "com_endpoint_openai_pres": "Liczba pomiędzy -2,0 a 2,0. Dodatnie wartości karzą nowe tokeny w oparciu o to, czy pojawiły się już w tekście, co zwiększa tendencję modelu do poruszania nowych tematów.", "com_endpoint_openai_prompt_prefix_placeholder": "Ustaw własne instrukcje do umieszczenia w systemowej wiadomości. Domyślnie: brak", "com_endpoint_openai_stop": "Do 4 sek<PERSON><PERSON><PERSON>, gdzie API przestanie generować dalsze tokeny.", "com_endpoint_openai_temp": "Wyższe wartości oznaczają wi<PERSON><PERSON><PERSON><PERSON>, natomiast niższe wartości prowadzą do bardziej skoncentrowanych i deterministycznych wyników. Zalecamy dostosowanie tej wartości lub Top P, ale nie obu jednocześnie.", "com_endpoint_openai_topp": "Alternatywa dla próbkowania z temperaturą, nazywana próbkowaniem jądra, gdzie model rozważa wyniki tokenów z prawdopodobieństwem top_p. <PERSON><PERSON>, 0,1 oznacza, że tylko tokeny składające się z 10% najwyższego prawdopodobieństwa są rozważane. Zalecamy dostosowanie tej wartości lub temperatury, ale nie obu jednocześnie.", "com_endpoint_output": "Wyjście", "com_endpoint_plug_image_detail": "Szczegóły obrazu", "com_endpoint_plug_resend_files": "Wyślij ponownie pliki", "com_endpoint_plug_set_custom_instructions_for_gpt_placeholder": "Ustaw własne instrukcje do umieszczenia w systemowej wiadomości. Domyślnie: brak", "com_endpoint_plug_skip_completion": "Pomiń uzupełnienie", "com_endpoint_plug_use_functions": "<PERSON><PERSON><PERSON><PERSON>", "com_endpoint_presence_penalty": "Kara za obecność", "com_endpoint_preset": "preset", "com_endpoint_preset_default": "jest teraz do<PERSON> presetem.", "com_endpoint_preset_default_item": "Domyślny:", "com_endpoint_preset_default_none": "Brak aktywnego domyślnego presetu.", "com_endpoint_preset_default_removed": "nie jest już do<PERSON> presetem.", "com_endpoint_preset_delete_confirm": "<PERSON>zy na pewno chcesz usunąć ten preset?", "com_endpoint_preset_delete_error": "Wys<PERSON>ą<PERSON>ł błąd podczas usuwania presetu. Spróbuj ponownie.", "com_endpoint_preset_import": "Preset zaimportowany!", "com_endpoint_preset_import_error": "Wystąpił błąd podczas importowania presetu. Spróbuj ponownie.", "com_endpoint_preset_name": "Nazwa <PERSON>wi<PERSON>", "com_endpoint_preset_save_error": "Wystą<PERSON>ł błąd podczas zapisywania presetu. Spróbuj ponownie.", "com_endpoint_preset_selected": "Preset aktywny!", "com_endpoint_preset_selected_title": "Aktywny!", "com_endpoint_preset_title": "Preset", "com_endpoint_presets": "presety", "com_endpoint_prompt_cache": "Użyj buforowania promptów", "com_endpoint_prompt_prefix": "Prefiks promptu", "com_endpoint_prompt_prefix_assistants": "Dodatkowe instrukcje", "com_endpoint_prompt_prefix_placeholder": "Ustaw niestandardowe instrukcje lub kontekst. <PERSON><PERSON><PERSON><PERSON>, je<PERSON><PERSON> puste.", "com_endpoint_reasoning_effort": "Wysiłek rozumowania", "com_endpoint_save_as_preset": "<PERSON>ap<PERSON>z jako predefin<PERSON>wane ustawi<PERSON>e", "com_endpoint_search": "Wyszukaj punkt końcowy po nazwie", "com_endpoint_set_custom_name": "Ustaw własną nazwę, w razie potrzeby odszukania tego ustawienia", "com_endpoint_skip_hover": "Omijaj etap uzupełnienia sprawdzający ostateczną odpowiedź i generowane kroki", "com_endpoint_stop": "Stop", "com_endpoint_stop_placeholder": "Oddziel wartości naciskając `Enter`", "com_endpoint_temperature": "Temperatura", "com_endpoint_top_k": "Top K", "com_endpoint_top_p": "Top P", "com_endpoint_use_active_assistant": "Użyj aktywnego asystenta", "com_error_expired_user_key": "Podany klucz dla {{0}} wygasł w {{1}}. <PERSON>szę podać nowy klucz i spróbować ponownie.", "com_error_files_dupe": "Wykryto zduplikowany plik.", "com_error_files_empty": "Puste pliki nie są dozwolone.", "com_error_files_process": "<PERSON><PERSON><PERSON><PERSON><PERSON>ł błąd podczas przetwarzania pliku.", "com_error_files_upload": "Wystą<PERSON>ł błąd podczas przesyłania pliku.", "com_error_files_upload_canceled": "Żądanie przesłania pliku zostało anulowane. Uwaga: przesyłanie pliku może nadal być przetwarzane i będzie wymagało ręcznego usunięcia.", "com_error_files_validation": "Wystą<PERSON>ł błąd podczas walidacji pliku.", "com_error_input_length": "Liczba tokenów najnowszej wiadomości jest zbyt duża, przekrac<PERSON>j<PERSON><PERSON> limit tokenów ({{0}}). <PERSON><PERSON><PERSON> skrócić swoją wiadomo<PERSON>, dopasuj maksymalny rozmiar kontekstu z parametrów rozmowy lub rozgałęź rozmowę, aby kontynuować.", "com_error_invalid_user_key": "Podano nieprawidłowy klucz. Podaj prawidłowy klucz i spróbuj ponownie.", "com_error_moderation": "Wygląda na to, że przesłana treść została oznaczona przez nasz system moderacji jako niezgodna z naszymi wytycznymi społeczności. Nie możemy kontynuować z tym konkretnym tematem. <PERSON><PERSON><PERSON> masz inne pytania lub tematy do omówienia, proszę edytuj swoją wiadomość lub utwórz nową rozmowę.", "com_error_no_base_url": "Nie znaleziono podstawowego URL. Podaj go i spróbuj ponownie.", "com_error_no_user_key": "Nie znaleziono klucza. Podaj klucz i spróbuj ponownie.", "com_files_filter": "Filtruj pliki...", "com_files_no_results": "Brak wyników.", "com_files_number_selected": "{{0}} z {{1}} elementów wybranych", "com_generated_files": "Wygenerowane pliki:", "com_hide_examples": "<PERSON>k<PERSON><PERSON>", "com_nav_account_settings": "Ustawienia konta", "com_nav_always_make_prod": "Zawsze twórz nowe wersje produkcyjne", "com_nav_archive_created_at": "Utworzono", "com_nav_archive_name": "Nazwa", "com_nav_archived_chats": "Zarchiwizowane rozmowy", "com_nav_at_command": "Polecenie @", "com_nav_at_command_description": "Przełącz polecenie \"@\" do przełączania punktów końcowych, modeli, presetów, itp.", "com_nav_audio_play_error": "Błąd odtwarzania audio: {{0}}", "com_nav_audio_process_error": "Błąd przetwarzania audio: {{0}}", "com_nav_auto_scroll": "Automatyczne przewijanie do najnowszej wiadomości przy otwarciu czatu", "com_nav_auto_send_prompts": "Automatycznie wysyłaj prompty", "com_nav_auto_send_text": "Automatycznie wysyłaj tekst", "com_nav_auto_send_text_disabled": "ustaw -1 aby wyłączyć", "com_nav_auto_transcribe_audio": "Automatycznie transkrybuj audio", "com_nav_automatic_playback": "Automatyczne odtwarzanie najnowszej wiadomości", "com_nav_balance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_browser": "Przeglądarka", "com_nav_change_picture": "Zmień zdjęcie", "com_nav_chat_commands": "Polecenia czatu", "com_nav_chat_commands_info": "Te polecenia są aktywowane przez wpisanie określonych znaków na początku twojej wiadomości. Każde polecenie jest uruchamiane przez wyznaczony prefiks. Możesz je wył<PERSON>, je<PERSON><PERSON> często używasz tych znaków do rozpoczynania wiadomości.", "com_nav_chat_direction": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_clear_all_chats": "Usuń wszystkie konwersacje", "com_nav_clear_conversation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rozmowę", "com_nav_clear_conversation_confirm_message": "Czy na pewno chcesz usunąć wszystkie konwersacje? Tej operacji nie można cofnąć.", "com_nav_close_sidebar": "Zamknij pasek boczny", "com_nav_commands": "Polecenia", "com_nav_confirm_clear": "Potwierdź usunięcie", "com_nav_conversation_mode": "<PERSON><PERSON>", "com_nav_convo_menu_options": "<PERSON><PERSON><PERSON> <PERSON> roz<PERSON>wy", "com_nav_db_sensitivity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> decybel<PERSON>", "com_nav_delete_account": "Us<PERSON>ń konto", "com_nav_delete_account_button": "Trwale usuń moje konto", "com_nav_delete_account_confirm": "<PERSON><PERSON><PERSON><PERSON> konto - j<PERSON><PERSON> pewien?", "com_nav_delete_account_email_placeholder": "Proszę wprow<PERSON><PERSON><PERSON> email konta", "com_nav_delete_cache_storage": "Usuń pamięć podręczną TTS", "com_nav_delete_data_info": "Wszystkie twoje dane zostaną usunięte.", "com_nav_delete_warning": "OSTRZEŻENIE: To trwale usunie twoje konto.", "com_nav_enable_cache_tts": "Włącz pamięć podręczną TTS", "com_nav_enable_cloud_browser_voice": "Użyj głosów opartych na chmurze", "com_nav_enabled": "Włą<PERSON><PERSON>", "com_nav_engine": "Silnik", "com_nav_enter_to_send": "<PERSON><PERSON><PERSON><PERSON><PERSON>, a<PERSON> w<PERSON><PERSON><PERSON> w<PERSON>", "com_nav_export_all_message_branches": "Eksportuj wszystkie gałęzie wiadomości", "com_nav_export_conversation": "Eksportuj konwersację", "com_nav_export_filename": "Nazwa pliku", "com_nav_export_filename_placeholder": "Podaj nazwę pliku", "com_nav_export_include_endpoint_options": "Dołącz opcje punktu końcowego", "com_nav_export_recursive": "Rekurencyjny", "com_nav_export_recursive_or_sequential": "Re<PERSON>rencyjny czy sekwencyjny?", "com_nav_export_type": "<PERSON><PERSON>", "com_nav_external": "Zewnętrzny", "com_nav_font_size": "Rozmiar <PERSON>ki", "com_nav_font_size_base": "Średni", "com_nav_font_size_lg": "<PERSON><PERSON><PERSON>", "com_nav_font_size_sm": "Ma<PERSON><PERSON>", "com_nav_font_size_xl": "<PERSON><PERSON><PERSON>", "com_nav_font_size_xs": "<PERSON><PERSON><PERSON>ły", "com_nav_help_faq": "Pomoc i często zadawane pytania", "com_nav_hide_panel": "<PERSON>k<PERSON>j s<PERSON>jny prawy panel", "com_nav_info_code_artifacts": "Włącza wyświetlanie eksperymentalnych artefaktów kodu obok czatu", "com_nav_info_custom_prompt_mode": "<PERSON><PERSON>, domyślny systemowy prompt artefaktów nie zostanie dołączony. Wszystkie instrukcje generowania artefaktów muszą być podane ręcznie w tym trybie.", "com_nav_info_enter_to_send": "<PERSON><PERSON> w<PERSON>, naci<PERSON><PERSON><PERSON><PERSON> `ENTER` wyśle twoją wiadomość. <PERSON><PERSON> wyłączone, naciśnięcie Enter doda nową linię, a do wysłania wiadomości będ<PERSON> `CTRL + ENTER` / `⌘ + ENTER`.", "com_nav_info_include_shadcnui": "Gdy włączone, instrukcje dotyczące używania komponentów shadcn/ui zostaną dołączone. shadcn/ui to kolekcja komponentów wielokrotnego użytku zbudowanych przy użyciu Radix UI i Tailwind CSS. Uwaga: są to obszerne instrukcje, powinieneś je włączyć tylko wtedy, gdy poinformowanie LLM o prawidłowych importach i komponentach jest dla ciebie ważne. Aby uzyskać więcej informacji o tych komponentach, odwiedź: https://ui.shadcn.com/", "com_nav_info_latex_parsing": "<PERSON><PERSON>, kod LaTeX w wiadomościach będzie renderowany jako równania matematyczne. Wyłączenie tego może poprawić w<PERSON>, jeśli nie potrzebujesz renderowania LaTeX.", "com_nav_info_save_draft": "<PERSON>dy włączone, tekst i załączniki, które wprowadzasz w formularzu czatu, będą automatycznie zapisywane lokalnie jako szkice. Te szkice będą dostępne nawet po przeładowaniu strony lub przełączeniu się na inną rozmowę. Szkice są przechowywane lokalnie na twoim urządzeniu i są usuwane po wysłaniu wiadomości.", "com_nav_info_show_thinking": "<PERSON><PERSON>, czat będzie domyślnie wyświetlał rozwijane menu myślenia, pozwalając na podgląd rozumowania AI w czasie rzeczywistym. <PERSON><PERSON> wyłączone, rozwijane menu myślenia pozostaną domyślnie zamknięte dla czystszego i bardziej uporządkowanego interfejsu", "com_nav_lang_arabic": "العربية", "com_nav_lang_auto": "Automatyczne wykrywanie", "com_nav_lang_brazilian_portuguese": "Portuguê<PERSON>", "com_nav_lang_chinese": "中文", "com_nav_lang_dutch": "Nederlands", "com_nav_lang_english": "English", "com_nav_lang_estonian": "<PERSON><PERSON><PERSON> keel", "com_nav_lang_finnish": "<PERSON><PERSON>", "com_nav_lang_french": "Français ", "com_nav_lang_georgian": "ქართული", "com_nav_lang_german": "De<PERSON>ch", "com_nav_lang_hebrew": "עברית", "com_nav_lang_indonesia": "Indonesia", "com_nav_lang_italian": "Italiano", "com_nav_lang_japanese": "日本語", "com_nav_lang_korean": "한국어", "com_nav_lang_polish": "<PERSON><PERSON>", "com_nav_lang_portuguese": "Português", "com_nav_lang_russian": "Русский", "com_nav_lang_spanish": "Español", "com_nav_lang_swedish": "Svenska", "com_nav_lang_thai": "ไทย", "com_nav_lang_traditional_chinese": "繁體中文", "com_nav_lang_turkish": "Türkçe", "com_nav_lang_vietnamese": "Tiếng <PERSON>", "com_nav_language": "Język", "com_nav_latex_parsing": "Parsowanie LaTeX w wiadomościach (może wpływać na wydajność)", "com_nav_log_out": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_long_audio_warning": "Dłuższe teksty będą potrzebować więcej czasu na przetworzenie.", "com_nav_maximize_chat_space": "Maksymalizuj przestrzeń czatu", "com_nav_modular_chat": "Włącz przełączanie punktów końcowych w trakcie rozmowy", "com_nav_my_files": "<PERSON><PERSON>", "com_nav_not_supported": "Nieobsługiwane", "com_nav_open_sidebar": "Otwórz pasek boczny", "com_nav_playback_rate": "Szybkość odtwarzania audio", "com_nav_plugin_auth_error": "Wystąpił błąd podczas próby uwierzytelnienia tej wtyczki. Proszę spróbować ponownie.", "com_nav_plugin_install": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_plugin_search": "Wyszukiwanie wtyczek", "com_nav_plugin_store": "Sklep z wtyczkami", "com_nav_plugin_uninstall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_plus_command": "Polecenie +", "com_nav_plus_command_description": "Przełącz polecenie \"+\" do dodawania ustawienia wielu odpowiedzi", "com_nav_profile_picture": "<PERSON><PERSON><PERSON><PERSON><PERSON> profilow<PERSON>", "com_nav_save_drafts": "Zapisuj szkice lokalnie", "com_nav_scroll_button": "Przycisk przewijania do końca", "com_nav_search_placeholder": "<PERSON><PERSON><PERSON> w<PERSON>dom<PERSON>", "com_nav_send_message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_setting_account": "Ko<PERSON>", "com_nav_setting_beta": "Funkcje beta", "com_nav_setting_chat": "<PERSON><PERSON><PERSON>", "com_nav_setting_data": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_setting_general": "Ogólne", "com_nav_setting_speech": "<PERSON><PERSON>", "com_nav_settings": "Ustawienia", "com_nav_shared_links": "Linki udostępnione", "com_nav_show_code": "Zawsze pokazuj kod podczas używania interpretera kodu", "com_nav_show_thinking": "Domyślnie otwieraj rozwijane menu myślenia", "com_nav_slash_command": "Polecenie /", "com_nav_slash_command_description": "Prz<PERSON>ł<PERSON><PERSON> polecenie \"/\" do wybierania promptu za pomocą klawiatury", "com_nav_speech_to_text": "Mowa na tekst", "com_nav_stop_generating": "Zatrzymaj generowanie", "com_nav_text_to_speech": "Tekst na mowę", "com_nav_theme": "Motyw", "com_nav_theme_dark": "Ciemny", "com_nav_theme_light": "<PERSON><PERSON><PERSON>", "com_nav_theme_system": "Domyślny", "com_nav_tool_dialog": "Narzędzia asystenta", "com_nav_tool_dialog_agents": "Narzędzia agenta", "com_nav_tool_dialog_description": "Asystent musi zostać zapisany, aby zachować wybrane narzędzia.", "com_nav_tool_remove": "Usuń", "com_nav_tool_search": "Wyszukaj narzędzia", "com_nav_user": "Użytkownik", "com_nav_user_msg_markdown": "Renderuj wiadomości użytkownika jako markdown", "com_nav_user_name_display": "Wyświetlaj nazwę użytkownika w wiadomościach", "com_nav_voice_select": "<PERSON><PERSON><PERSON>", "com_show_agent_settings": "Pokaż ustawienia agenta", "com_show_completion_settings": "Pokaż ustawienia uzupełniania", "com_show_examples": "Pokaż przykłady", "com_sidepanel_agent_builder": "Kreator agenta", "com_sidepanel_assistant_builder": "Kreator Asystenta", "com_sidepanel_attach_files": "Załącz Pliki", "com_sidepanel_conversation_tags": "Zakład<PERSON>", "com_sidepanel_hide_panel": "Ukryj <PERSON>", "com_sidepanel_manage_files": "Zarządzaj <PERSON>ami", "com_sidepanel_parameters": "Parametry", "com_ui_2fa_account_security": "2FA dodaje dodatkową warstwę bezpieczeństwa do twojego konta.", "com_ui_2fa_disable": " Wyłącz 2FA", "com_ui_2fa_disable_error": "Błąd podczas wyłączania 2FA", "com_ui_2fa_disabled": "2FA zostało wyłączone", "com_ui_2fa_enable": "Włącz 2FA", "com_ui_2fa_enabled": "2FA zostało włączone", "com_ui_accept": "<PERSON>k<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_add": "<PERSON><PERSON><PERSON>", "com_ui_add_model_preset": "Dodaj model lub preset dla do<PERSON><PERSON><PERSON><PERSON><PERSON> od<PERSON><PERSON><PERSON>", "com_ui_add_multi_conversation": "Dodaj wielokrotną konwersację", "com_ui_admin": "Administrator", "com_ui_admin_access_warning": "Wyłączenie dostępu administratora do tej funkcji może spowodować nieoczekiwane problemy z interfejsem użytkownika wymagające odświeżenia. Je<PERSON><PERSON> zostanie zapisane, jedynym sposobem na przywrócenie jest ustawienie interfejsu w konfiguracji librechat.yaml, które wpływa na wszystkie role.", "com_ui_admin_settings": "Ustawienia administratora", "com_ui_advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_agent": "Agent", "com_ui_agent_delete_error": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> błąd podczas usuwania agenta", "com_ui_agent_deleted": "Pomyślnie usunięto agenta", "com_ui_agent_duplicate_error": "<PERSON><PERSON><PERSON><PERSON><PERSON>ł błąd podczas duplikowania agenta", "com_ui_agent_duplicated": "Pomyślnie zduplikowano agenta", "com_ui_agent_editing_allowed": "Inni użytkownicy mogą już edytować tego agenta", "com_ui_agents": "<PERSON><PERSON><PERSON>", "com_ui_agents_allow_create": "Zezwól na tworzenie agentów", "com_ui_agents_allow_share_global": "Zezwól na udostępnianie agentów wszystkim użytkownikom", "com_ui_agents_allow_use": "Zezwól na używanie agentów", "com_ui_all": "wszystkie", "com_ui_all_proper": "Wszystkie", "com_ui_archive": "Archiwu<PERSON>", "com_ui_archive_error": "Nie udało się archiwizować rozmowy", "com_ui_artifact_click": "Kliknij, aby otworzyć", "com_ui_artifacts": "Artefakty", "com_ui_artifacts_toggle": "Przełącz interfejs artefaktów", "com_ui_ascending": "Rosnąco", "com_ui_assistant": "Asystent", "com_ui_assistant_delete_error": "<PERSON><PERSON><PERSON><PERSON><PERSON>ł błąd podczas usuwania asystenta", "com_ui_assistant_deleted": "Pomyślnie usunięto asystenta", "com_ui_assistants": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_assistants_output": "Wyjście asystentów", "com_ui_attach_error": "Nie można dołączyć pliku. Utwórz lub wybierz konwersację lub spróbuj odświeżyć stronę.", "com_ui_attach_error_openai": "Nie można dołączyć plików Asystenta do innych punktów końcowych", "com_ui_attach_error_size": "Przekroczono limit rozmiaru pliku dla punktu końcowego:", "com_ui_attach_error_type": "Nieobsługiwany typ pliku dla punktu końcowego:", "com_ui_attach_warn_endpoint": "Pliki inne niż asystenta mogą być ignorowane bez kompatybilnego narzędzia", "com_ui_attachment": "Załącznik", "com_ui_authentication": "Uwierzytelnianie", "com_ui_avatar": "<PERSON><PERSON><PERSON>", "com_ui_back_to_chat": "Powrót do czatu", "com_ui_back_to_prompts": "Powrót do promptów", "com_ui_bookmark_delete_confirm": "<PERSON>zy na pewno chcesz usunąć tę zakładkę?", "com_ui_bookmarks": "Zakład<PERSON>", "com_ui_bookmarks_add": "<PERSON><PERSON><PERSON>", "com_ui_bookmarks_add_to_conversation": "Dodaj do bieżącej rozmowy", "com_ui_bookmarks_count": "Licznik", "com_ui_bookmarks_create_error": "Wystą<PERSON>ł błąd podczas tworzenia zakładki", "com_ui_bookmarks_create_exists": "Ta zakładka już istnieje", "com_ui_bookmarks_create_success": "Zakładka została pomyślnie utworzona", "com_ui_bookmarks_delete": "<PERSON><PERSON><PERSON> zakładkę", "com_ui_bookmarks_delete_error": "Wys<PERSON>ą<PERSON>ł błąd podczas usuwania zakładki", "com_ui_bookmarks_delete_success": "Zakładka została pomyślnie usunięta", "com_ui_bookmarks_description": "Opis", "com_ui_bookmarks_edit": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_bookmarks_filter": "Filtruj zakładki...", "com_ui_bookmarks_new": "Nowa zakładka", "com_ui_bookmarks_title": "<PERSON><PERSON><PERSON>", "com_ui_bookmarks_update_error": "Wystąpił błąd podczas aktualizacji zakładki", "com_ui_bookmarks_update_success": "Zakładka została pomyślnie zaktualizowana", "com_ui_bulk_delete_error": "Nie udało się usunąć udostępnionych linków", "com_ui_cancel": "<PERSON><PERSON><PERSON>", "com_ui_chat": "<PERSON><PERSON><PERSON>", "com_ui_chat_history": "Historia <PERSON>tu", "com_ui_clear_all": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wszystko", "com_ui_code": "Kod", "com_ui_collapse_chat": "<PERSON><PERSON><PERSON> czat", "com_ui_command_placeholder": "Opcjonalnie: W<PERSON><PERSON><PERSON>ź polecenie dla promptu lub użyj nazwy", "com_ui_command_usage_placeholder": "<PERSON><PERSON><PERSON><PERSON> prompt <PERSON><PERSON><PERSON> polecenia lub nazwy", "com_ui_confirm_action": "Potwierdź działanie", "com_ui_confirm_admin_use_change": "Zmiana tego ustawienia zablokuje dostęp dla administratorów, w tym Ciebie. Czy na pewno chcesz kontynuować?", "com_ui_confirm_change": "Potwierdź zmianę", "com_ui_context": "Kontekst", "com_ui_continue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_controls": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_copied": "Skopiowano!", "com_ui_copied_to_clipboard": "Skopiowano do schowka", "com_ui_copy_code": "<PERSON><PERSON><PERSON><PERSON> kod", "com_ui_copy_link": "Skopiuj link", "com_ui_copy_to_clipboard": "Kopiuj do schowka", "com_ui_create": "Utwórz", "com_ui_create_link": "Utwórz link", "com_ui_create_prompt": "<PERSON><PERSON><PERSON><PERSON>rz prompt", "com_ui_currently_production": "Aktualnie w produkcji", "com_ui_custom_prompt_mode": "<PERSON>b niestandardowego promptu", "com_ui_dashboard": "Panel", "com_ui_date": "Data", "com_ui_date_april": "Kwiecień", "com_ui_date_august": "Sierpień", "com_ui_date_december": "Grudzień", "com_ui_date_february": "<PERSON><PERSON>", "com_ui_date_january": "Styczeń", "com_ui_date_july": "Lipiec", "com_ui_date_june": "Czerwiec", "com_ui_date_march": "Marzec", "com_ui_date_may": "Maj", "com_ui_date_november": "Listopad", "com_ui_date_october": "Październik", "com_ui_date_previous_30_days": "Poprzednie 30 dni", "com_ui_date_previous_7_days": "Poprzednie 7 dni", "com_ui_date_september": "Wrzesień", "com_ui_date_today": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_date_yesterday": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_decline": "<PERSON><PERSON> a<PERSON>", "com_ui_delete": "Usuń", "com_ui_delete_action": "<PERSON><PERSON><PERSON> akcję", "com_ui_delete_action_confirm": "<PERSON>zy na pewno chcesz usunąć tę akcję?", "com_ui_delete_agent_confirm": "<PERSON>zy na pewno chcesz usunąć tego agenta?", "com_ui_delete_assistant_confirm": "Czy na pewno chcesz usunąć tego Asystenta? Tej operacji nie można cofnąć.", "com_ui_delete_confirm": "Spowoduje to usunięcie", "com_ui_delete_confirm_prompt_version_var": "<PERSON><PERSON><PERSON><PERSON><PERSON> to usunięcie wybranej wersji dla \"{{0}}.\" <PERSON><PERSON><PERSON> nie istnieją inne wersje, prompt zost<PERSON>e us<PERSON>.", "com_ui_delete_conversation": "<PERSON><PERSON><PERSON><PERSON> czat?", "com_ui_delete_prompt": "<PERSON><PERSON><PERSON><PERSON> prompt?", "com_ui_delete_tool": "Usuń narzędzie", "com_ui_delete_tool_confirm": "<PERSON>zy na pewno chcesz usunąć to narzędzie?", "com_ui_descending": "Malejąco", "com_ui_description": "Opis", "com_ui_description_placeholder": "Opcjonalnie: Wprowadź opis do wyświetlenia dla promptu", "com_ui_download": "<PERSON><PERSON><PERSON>", "com_ui_download_artifact": "Pobierz artefakt", "com_ui_download_error": "Błąd pobierania pliku. Plik mógł zostać usunięty.", "com_ui_dropdown_variables": "Zmienne rozwijane:", "com_ui_dropdown_variables_info": "Tw<PERSON>rz własne menu rozwijane dla swoich promptów: `{{nazwa_zmiennej:opcja1|opcja2|opcja3}}`", "com_ui_duplicate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_duplication_error": "Wys<PERSON>ą<PERSON>ł błąd podczas duplikowania konwersacji", "com_ui_duplication_processing": "Duplikowanie konwersacji...", "com_ui_duplication_success": "Pomyślnie zduplikowano konwersację", "com_ui_edit": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_endpoint": "Punkt końcowy", "com_ui_endpoint_menu": "Menu punktu końcowego LLM", "com_ui_enter": "Wprowadź", "com_ui_enter_api_key": "Wprowadź klucz API", "com_ui_enter_openapi_schema": "Wprowadź swoją schemę OpenAPI tutaj", "com_ui_error": "Błąd", "com_ui_error_connection": "Błąd połączenia z serwerem, spróbuj odświeżyć stronę.", "com_ui_error_save_admin_settings": "<PERSON><PERSON><PERSON><PERSON><PERSON>ł błąd podczas zapisywania ustawień administratora.", "com_ui_examples": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_export_convo_modal": "Eksportuj okno rozmowy", "com_ui_field_required": "To pole jest wymagane", "com_ui_filter_prompts": "Filtruj prompty", "com_ui_filter_prompts_name": "Filtruj prompty po nazwie", "com_ui_fork": "Rozgałęź", "com_ui_fork_all_target": "Dołącz wszystko do/z tego miejsca", "com_ui_fork_branches": "Dołącz powiązane gałęzie", "com_ui_fork_change_default": "Domyślna opcja rozgałęzienia", "com_ui_fork_default": "Użyj domyślnej opcji rozgałęzienia", "com_ui_fork_error": "Wystąpił błąd podczas rozgałęziania konwersacji", "com_ui_fork_from_message": "<PERSON><PERSON><PERSON><PERSON> opcję rozgałęzienia", "com_ui_fork_info_1": "Użyj tego ustawienia, aby roz<PERSON><PERSON><PERSON><PERSON>ć wiadomości z pożądanym zachowaniem.", "com_ui_fork_info_2": "\"Rozgałęzianie\" odnosi się do tworzenia nowej rozmowy, która zaczyna/kończy się od określonych wiadomości w bieżącej rozmowie, tworząc kopię zgodnie z wybranymi opcjami.", "com_ui_fork_info_3": "\"Wiadomość docelowa\" odnosi się do wiadomości, z której otwarto to okno, lub, je<PERSON><PERSON> zaznaczy<PERSON> \"{{0}}\", do najnowszej wiadomości w rozmowie.", "com_ui_fork_info_branches": "Ta opcja rozgałęzia widoczne wiadomości wraz z powiązanymi gałęziami; innymi słowy, bezpośrednią ścieżkę do wiadomości docelowej, włączając gałęzie wzdłuż ścieżki.", "com_ui_fork_info_remember": "Zaznacz to, aby zapamiętać wybrane opcje do przyszłego użycia, ułatwiając szybsze rozgałęzianie rozmów według preferencji.", "com_ui_fork_info_start": "<PERSON><PERSON><PERSON>, rozgałęzianie rozpocznie się od tej wiadomości do najnowszej wiadomości w rozmowie, zgodnie z wybranym zachowaniem powyżej.", "com_ui_fork_info_target": "Ta opcja rozgałęzia wszystkie wiadomości prowadzące do wiadomości docelowej, włączając jej sąsiadów; innymi słow<PERSON>, wszystkie gałęzie wiadomości, niezależnie od tego, czy są widoczne czy wzdłuż tej samej ś<PERSON>żki, są włączone.", "com_ui_fork_info_visible": "Ta opcja rozgałęzia tylko widoczne wiadomości; innymi s<PERSON>, bezpośrednią ścieżkę do wiadomości docelowej, bez żadnych gałęzi.", "com_ui_fork_processing": "Rozgałęzianie konwersacji...", "com_ui_fork_remember": "Zapamiętaj", "com_ui_fork_remember_checked": "Twój wybór zostanie zapamiętany po użyciu. Zmień to w dowolnym momencie w ustawieniach.", "com_ui_fork_split_target": "Rozpocznij rozgałęzienie tutaj", "com_ui_fork_split_target_setting": "Domyślnie rozpocznij rozgałęzienie od docelowej wiadomości", "com_ui_fork_success": "Pomyślnie rozgałęziono konwersację", "com_ui_fork_visible": "Tylko widoczne wiadomości", "com_ui_go_to_conversation": "Przejdź do rozmowy", "com_ui_happy_birthday": "To moje pierwsze urodziny!", "com_ui_hide_qr": "Ukryj kod QR", "com_ui_host": "Host", "com_ui_image_gen": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "com_ui_import_conversation_error": "Wystąpił błąd podczas importowania konwersacji", "com_ui_import_conversation_file_type_error": "Nieobsługiwany typ importu", "com_ui_import_conversation_info": "Importuj konwersacje z pliku JSON", "com_ui_import_conversation_success": "Konwersacje zostały pomyślnie zaimportowane", "com_ui_include_shadcnui": "Dołącz instrukcje komponentów shadcn/ui", "com_ui_input": "Wprowadź", "com_ui_instructions": "Instrukcje", "com_ui_latest_footer": "Każde AI dla wszystkich.", "com_ui_latest_production_version": "Najnowsza wersja produkcyjna", "com_ui_latest_version": "Najn<PERSON><PERSON> wersja", "com_ui_librechat_code_api_key": "Uzyskaj klucz API interpretera kodu LibreChat", "com_ui_librechat_code_api_subtitle": "Bezpieczny. Wielojęzyczny. Pliki wejściowe/wyjściowe.", "com_ui_librechat_code_api_title": "Uruchom kod AI", "com_ui_locked": "Zablokowane", "com_ui_logo": "Logo {{0}}", "com_ui_manage": "Zarządzaj", "com_ui_max_tags": "<PERSON><PERSON><PERSON><PERSON><PERSON> dozwolona liczba to {{0}}, używane są najnows<PERSON> wartoś<PERSON>.", "com_ui_mention": "Wspomnij punkt końcowy, asystenta lub preset, aby szybko się przeł<PERSON>ć", "com_ui_min_tags": "<PERSON>e mo<PERSON>na us<PERSON> więce<PERSON>, wymagane minimum to {{0}}.", "com_ui_model": "Model", "com_ui_model_parameters": "Parametry modelu", "com_ui_more_info": "Więcej informacji", "com_ui_my_prompts": "<PERSON><PERSON> prompty", "com_ui_name": "Nazwa", "com_ui_new_chat": "<PERSON><PERSON> czat", "com_ui_next": "Następny", "com_ui_no": "<PERSON><PERSON>", "com_ui_no_bookmarks": "Wygląda na to, że nie masz jeszcze żadnych zakładek. Kliknij na czat i dodaj nową", "com_ui_no_category": "<PERSON>rak ka<PERSON>ii", "com_ui_no_changes": "Brak zmian do aktualizacji", "com_ui_no_terms_content": "Brak treści warunków użytkowania do wyświetlenia", "com_ui_nothing_found": "Nic nie znaleziono", "com_ui_of": "z", "com_ui_off": "Wyłączone", "com_ui_on": "Włą<PERSON><PERSON>", "com_ui_page": "Strona", "com_ui_prev": "Poprzedni", "com_ui_preview": "Podgląd", "com_ui_privacy_policy": "Polityka p<PERSON>watności", "com_ui_privacy_policy_url": "URL polityki prywatności", "com_ui_prompt": "Prompt", "com_ui_prompt_already_shared_to_all": "Ten prompt jest już udostępniony wszystkim użytkownikom", "com_ui_prompt_name": "Nazwa promptu", "com_ui_prompt_name_required": "<PERSON><PERSON>wa promptu jest wymagana", "com_ui_prompt_preview_not_shared": "Autor nie zezwolił na współpracę dla tego promptu.", "com_ui_prompt_text": "Tekst", "com_ui_prompt_text_required": "<PERSON><PERSON><PERSON> jest wymagany", "com_ui_prompt_update_error": "Wystą<PERSON>ł błąd podczas aktualizacji promptu", "com_ui_prompts": "Prompty", "com_ui_prompts_allow_create": "Zezwól na tworzenie promptów", "com_ui_prompts_allow_share_global": "Zezwól na udostępnianie promptów wszystkim użytkownikom", "com_ui_prompts_allow_use": "Zezwól na używanie promptów", "com_ui_provider": "Dostawca", "com_ui_read_aloud": "Przeczytaj na głos", "com_ui_refresh_link": "Odśwież link", "com_ui_regenerate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_region": "Region", "com_ui_rename": "Zmień nazwę", "com_ui_rename_prompt": "Zmień nazwę promptu", "com_ui_reset_var": "<PERSON>set<PERSON>j {{0}}", "com_ui_result": "<PERSON><PERSON><PERSON>", "com_ui_revoke": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_revoke_info": "Odwołaj wszystkie poświadczenia dostarczone przez użytkownika", "com_ui_revoke_key_confirm": "Czy na pewno chcesz odwołać ten klucz?", "com_ui_revoke_key_endpoint": "Od<PERSON><PERSON><PERSON> k<PERSON> dla {{0}}", "com_ui_revoke_keys": "Odwołaj <PERSON>", "com_ui_revoke_keys_confirm": "Czy na pewno chcesz odwołać wszystkie klucze?", "com_ui_role_select": "Rola", "com_ui_run_code": "Uruchom kod", "com_ui_run_code_error": "Wys<PERSON>ą<PERSON>ł błąd podczas uruchamiania kodu", "com_ui_save": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_save_submit": "Zapisz i wyślij", "com_ui_saved": "Zapisano!", "com_ui_schema": "<PERSON><PERSON><PERSON>", "com_ui_search": "Szukaj", "com_ui_select": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_select_file": "<PERSON><PERSON><PERSON>rz plik", "com_ui_select_model": "Wybierz model", "com_ui_select_provider": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_select_provider_first": "Najpierw wybierz dostaw<PERSON>ę", "com_ui_select_region": "Wybierz region", "com_ui_select_search_model": "Wyszukaj model po naz<PERSON>e", "com_ui_select_search_plugin": "Wyszukaj wtyczkę po nazwie", "com_ui_select_search_provider": "Wyszukaj dostawcę po nazwie", "com_ui_select_search_region": "Wyszukaj region po nazwie", "com_ui_share": "Udostępnij", "com_ui_share_create_message": "Twoje imię i jakiekolwiek wiadomości dodane po udostępnieniu pozostaną prywatne.", "com_ui_share_delete_error": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> błąd podczas usuwania udostępnionego linku.", "com_ui_share_error": "Wystąpił błąd podczas udostępniania linku do czatu", "com_ui_share_link_to_chat": "Udostępnij link w czacie", "com_ui_share_to_all_users": "Udostępnij wszystkim użytkownikom", "com_ui_share_update_message": "<PERSON><PERSON>, niestandardowe instrukcje i jakiekolwiek wiadomości dodane po udostępnieniu pozostaną prywatne.", "com_ui_share_var": "Udostępnij {{0}}", "com_ui_shared_link_bulk_delete_success": "Pomyślnie usunięto udostępnione linki", "com_ui_shared_link_delete_success": "Pomyślnie usunięto udostępniony link", "com_ui_shared_link_not_found": "Nie znaleziono linku udostępnionego", "com_ui_shared_prompts": "Udostępnione prompty", "com_ui_show_all": "Pokaż wszystko", "com_ui_show_qr": "Pokaż kod QR", "com_ui_simple": "Prosty", "com_ui_size": "Rozmiar", "com_ui_special_variables": "Zmienne specjalne:", "com_ui_speech_while_submitting": "Nie można przesłać mowy podczas generowania odpowiedzi", "com_ui_storage": "Przechowywanie", "com_ui_submit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_temporary": "Czat tymczasowy", "com_ui_terms_and_conditions": "Warunki użytkowania", "com_ui_terms_of_service": "Warunki korzystania z usługi", "com_ui_thinking": "Myślenie...", "com_ui_thoughts": "Przemyślenia", "com_ui_tools": "Narzędzia", "com_ui_unarchive": "Przywróć z archiwum", "com_ui_unarchive_error": "Nie udało się odtworzyć rozmowy z archiwum", "com_ui_unknown": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_update": "Aktualizuj", "com_ui_upload": "Prześ<PERSON>j", "com_ui_upload_code_files": "Prześlij do <PERSON>a kodu", "com_ui_upload_delay": "Prz<PERSON>yłanie \"{{0}}\" trwa dłużej niż przewidywano. <PERSON><PERSON><PERSON>, aż plik zakończy indeksowanie do pobrania.", "com_ui_upload_error": "Wystą<PERSON>ł błąd podczas przesyłania pliku", "com_ui_upload_file_search": "Prześlij do wyszukiwania plików", "com_ui_upload_files": "Prześlij pliki", "com_ui_upload_image": "Prześlij obraz", "com_ui_upload_image_input": "Prześlij obraz", "com_ui_upload_invalid": "Nieprawidłowy plik do przesłania. Musi być obrazem nieprzekraczającym limitu", "com_ui_upload_invalid_var": "Nieprawidłowy plik do przesłania. Musi być obrazem nieprzekraczającym {{0}} MB", "com_ui_upload_success": "Pomyślnie przesłano plik", "com_ui_upload_type": "Wybierz typ przesyłania", "com_ui_use_micrphone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_use_prompt": "<PERSON><PERSON><PERSON><PERSON>wi<PERSON>", "com_ui_variables": "Zmienne", "com_ui_variables_info": "Użyj podwójnych nawiasów klamrowych w tekście, aby utwo<PERSON><PERSON> zmienne, np. `{{przykładowa zmienna}}`, które później można wypełnić podczas używania promptu.", "com_ui_version_var": "<PERSON><PERSON><PERSON> {{0}}", "com_ui_versions": "<PERSON><PERSON><PERSON>", "com_ui_view_source": "Zobacz źródłowy czat", "com_ui_weekend_morning": "<PERSON><PERSON><PERSON>", "com_ui_yes": "Tak", "com_ui_zoom": "Powię<PERSON><PERSON>", "com_user_message": "Ty"}