{"com_a11y_ai_composing": "Yapay zeka hala yanıt oluşturuyor.", "com_a11y_end": "Yapay zeka yanıtını tamamladı.", "com_a11y_start": "Yapay zeka yanıtlamaya başladı.", "com_agents_allow_editing": "<PERSON><PERSON><PERSON> k<PERSON>anıcıların ajanınızı düzenlemesine izin verin", "com_agents_by_librechat": "LibreChat tarafından", "com_agents_code_interpreter": "Etkinleştirildiğinde, ajanınızın oluşturulan kodu çalıştırması ve dosya işleme dahil olmak üzere LibreChat Kod Yorumlayıcı API'sini güvenli bir şekilde kullanmasına olanak tanır. Geçerli bir API anahtarı gerektirir.", "com_agents_code_interpreter_title": "Kod Yorumlayıcı API", "com_agents_create_error": "Ajanın<PERSON>z oluşturulurken bir hata oluştu.", "com_agents_description_placeholder": "İsteğe bağlı: Ajanınız<PERSON> burada tanımlayın", "com_agents_enable_file_search": "Dosya Aramayı Etkinleştir", "com_agents_file_search_disabled": "<PERSON><PERSON><PERSON> i<PERSON>in dosya yüklemeden önce ajan oluş<PERSON>ulmalıdır.", "com_agents_file_search_info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, a<PERSON> listelenen dosya adlarından haberdar olacak ve bu dosyalardan ilgili içeriği alabilecektir.", "com_agents_instructions_placeholder": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ığı sistem talimatları", "com_agents_missing_provider_model": "Lütfen bir ajan o<PERSON>şturmadan önce bir sağlayıcı ve model seçin.", "com_agents_name_placeholder": "İsteğe bağlı: <PERSON><PERSON><PERSON><PERSON> adı", "com_agents_no_access": "Bu ajanı düzenleme erişiminiz yok.", "com_agents_not_available": "<PERSON><PERSON>", "com_agents_search_name": "Ajanları ada göre ara", "com_agents_update_error": "Ajanı<PERSON><PERSON>z güncellenirken bir hata oluştu.", "com_assistants_actions": "<PERSON><PERSON><PERSON>", "com_assistants_actions_disabled": "<PERSON><PERSON><PERSON> eklemeden önce bir asistan oluşturmanız gerekiyor.", "com_assistants_actions_info": "Asistanın API'leri kullanarak bilgi getirmesine veya eylem gerçekleştirmesine izin ver", "com_assistants_add_actions": "<PERSON><PERSON><PERSON>", "com_assistants_add_tools": "Araçları Ekle", "com_assistants_append_date": "<PERSON>u anki tarih ve saati e<PERSON>in", "com_assistants_append_date_tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, şu anki müşteri tarihi ve saati, Asistanın sistem talimatlarına eklenir.", "com_assistants_available_actions": "<PERSON><PERSON><PERSON>", "com_assistants_capabilities": "Yetenekler", "com_assistants_code_interpreter": "Kod Yorumlayıcı", "com_assistants_code_interpreter_files": "Aşağıdaki dosyalar yalnızca Kod Yorumlayıcı için kullanılabilir:", "com_assistants_code_interpreter_info": "<PERSON><PERSON>, asistanın kod yazmasına ve çalıştırmasına olanak tanır. Bu araç, çeşitli veri ve formatlara sahip dosyaları işleyebilir ve grafikler gibi dosyalar oluşturabilir.", "com_assistants_completed_action": "{{0}} ile kon<PERSON>", "com_assistants_completed_function": "{{0}} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_assistants_conversation_starters": "Konuşma Başlatıcıları", "com_assistants_conversation_starters_placeholder": "Bir konuşma başlatıcı girin", "com_assistants_create_error": "Asistanınızı oluşturma sırasında bir hata oluştu.", "com_assistants_create_success": "Başarıyla oluşturuldu", "com_assistants_delete_actions_error": "<PERSON><PERSON><PERSON> silme sı<PERSON>ında bir hata oluştu.", "com_assistants_delete_actions_success": "Asistandan E<PERSON>m ba<PERSON><PERSON><PERSON><PERSON> si<PERSON>", "com_assistants_description_placeholder": "Seçmeli: Asistanınızın açıklamasını buraya yazın", "com_assistants_domain_info": "Asistan bu bilgiyi {{0}} adresine g<PERSON>", "com_assistants_file_search": "<PERSON><PERSON><PERSON>", "com_assistants_file_search_info": "Dosya Araması için vektör mağazalarını eklemek henüz desteklenmiyor. Bunları Sağlayıcı Oyun Alanı'ndan ekleyebilir veya mesajlar için dosya ekleyerek konu bazında dosya arayabilirsin.", "com_assistants_function_use": "Asistan {{0}} kullandı", "com_assistants_image_vision": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_assistants_instructions_placeholder": "Asistanın kullandığı sistem talimatları", "com_assistants_knowledge": "<PERSON><PERSON><PERSON>", "com_assistants_knowledge_disabled": "Bilgi olarak dosya yüklemeden önce, Asistan oluşturulmalı ve Kod Yorumlayıcı veya Geri Getirme etkinleştirilip kaydedilmelidir.", "com_assistants_knowledge_info": "Dosyaları Bilgi <PERSON>, Asistan ile yapılan konuşmalar dosya içeriklerini içerebilir.", "com_assistants_max_starters_reached": "<PERSON><PERSON><PERSON><PERSON> konuşma başlatıcı sayısına ulaşıldı", "com_assistants_name_placeholder": "Seçmeli: as<PERSON><PERSON>n adı", "com_assistants_non_retrieval_model": "Dosya arama bu modelde etkin değ<PERSON>. Lütfen başka bir model se<PERSON><PERSON>.", "com_assistants_retrieval": "<PERSON><PERSON>", "com_assistants_running_action": "<PERSON><PERSON><PERSON>ışıyor", "com_assistants_search_name": "Asistan adında ara", "com_assistants_update_actions_error": "<PERSON><PERSON>m oluşturma veya güncelleme sırasında bir hata oluş<PERSON>.", "com_assistants_update_actions_success": "<PERSON><PERSON><PERSON> başarıyla oluşturuldu veya güncellendi", "com_assistants_update_error": "Asistanınızı güncelleme sırasında bir hata oluş<PERSON>.", "com_assistants_update_success": "Başarıyla gü<PERSON>llendi", "com_auth_already_have_account": "Zaten bir hesabınız var mı?", "com_auth_apple_login": "Apple ile Giriş <PERSON>", "com_auth_back_to_login": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>n", "com_auth_click": "Tıklayın", "com_auth_click_here": "Buraya tıklayın", "com_auth_continue": "<PERSON><PERSON> et", "com_auth_create_account": "Hesabınızı oluşturun", "com_auth_discord_login": "Discord ile devam et", "com_auth_email": "E-posta", "com_auth_email_address": "E-posta adresi", "com_auth_email_max_length": "E-posta 120 karakteri geçmemelidir", "com_auth_email_min_length": "E-posta en az 6 karakter olmalıdır", "com_auth_email_pattern": "Geçerli bir e-posta adresi girmelisiniz", "com_auth_email_required": "E-posta gereklidir", "com_auth_email_resend_link": "E-postayı yeniden gönder", "com_auth_email_resent_failed": "Doğrulama e-postasını yeniden gönderme başarısız oldu", "com_auth_email_resent_success": "Doğrulama e-postası başarıyla yeniden gönderildi", "com_auth_email_verification_failed": "E-posta doğrulama başarısız oldu", "com_auth_email_verification_failed_token_missing": "Doğ<PERSON><PERSON>a ba<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jeton eksik", "com_auth_email_verification_in_progress": "E-postanızı doğruluyoruz, lütfen bekleyin", "com_auth_email_verification_invalid": "Geçersiz e-posta doğrulama", "com_auth_email_verification_redirecting": "{{0}} saniye içinde yönlendiriliyor...", "com_auth_email_verification_resend_prompt": "E-postayı almadınız mı?", "com_auth_email_verification_success": "E-posta başarıyla doğrulandı", "com_auth_error_create": "Hesabınızı kaydetmeye çalışırken bir hata oluştu. Lütfen tekrar deneyin.", "com_auth_error_invalid_reset_token": "Bu şifre sıfırlama jetonu artık geçerli değil.", "com_auth_error_login": "Sağlanan bilgilerle giriş yapılamıyor. Lütfen kimlik bilgilerinizi kontrol edin ve tekrar deneyin.", "com_auth_error_login_ban": "Hesabınız hizmetimize yönelik ihlaller nedeniyle geçici olarak yasaklanmıştır.", "com_auth_error_login_rl": "<PERSON><PERSON>sa sürede çok fazla giriş denemesi yapıldı. Lütfen daha sonra tekrar deneyin.", "com_auth_error_login_server": "<PERSON><PERSON><PERSON> sunucu hatası oluştu. Lütfen birkaç dakika bekleyin ve tekrar deneyin.", "com_auth_error_login_unverified": "Hesabınız doğrulanmamış. Lütfen doğrulama bağlantısı için e-postanızı kontrol edin.", "com_auth_facebook_login": "Facebook ile devam et", "com_auth_full_name": "<PERSON> adı", "com_auth_github_login": "<PERSON><PERSON><PERSON> ile devam et", "com_auth_google_login": "Google ile devam et", "com_auth_here": "BURAYA", "com_auth_login": "<PERSON><PERSON><PERSON> yap", "com_auth_login_with_new_password": "Şimdi yeni şifreniz ile giriş yapabilirsiniz.", "com_auth_name_max_length": "Ad en fazla 80 karakter olmalıdır", "com_auth_name_min_length": "Ad en az 3 karakter olmalıdır", "com_auth_name_required": "Ad gereklidir", "com_auth_no_account": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z yok mu?", "com_auth_password": "Şifre", "com_auth_password_confirm": "<PERSON><PERSON><PERSON><PERSON>", "com_auth_password_forgot": "<PERSON><PERSON><PERSON>i unuttum?", "com_auth_password_max_length": "Şifre en fazla 128 karakter olmalıdır", "com_auth_password_min_length": "Şifre en az 8 karakter olmalıdır", "com_auth_password_not_match": "<PERSON><PERSON><PERSON><PERSON>", "com_auth_password_required": "<PERSON><PERSON><PERSON>", "com_auth_registration_success_generic": "E-posta adresinizi doğrulamak için lütfen e-postanızı kontrol edin.", "com_auth_registration_success_insecure": "<PERSON><PERSON><PERSON> baş<PERSON><PERSON><PERSON> tamamlandı.", "com_auth_reset_password": "Şifrenizi sıfırlayın", "com_auth_reset_password_if_email_exists": "Bu e-postaya sahip bir hesap varsa, e-posta ile şifre sıfırlama talimatları gönderilmiştir. Lütfen spam klasörünüzü kontrol edin.", "com_auth_reset_password_link_sent": "E-posta gö<PERSON>ildi", "com_auth_reset_password_success": "Şifre başarıyla sıfırlandı", "com_auth_sign_in": "<PERSON><PERSON><PERSON> yap", "com_auth_sign_up": "<PERSON><PERSON><PERSON>", "com_auth_submit_registration": "<PERSON><PERSON><PERSON> formunu g<PERSON>nder", "com_auth_to_reset_your_password": "şifrenizi sıfırlamak için.", "com_auth_to_try_again": "yeniden denemek <PERSON>.", "com_auth_username": "Kullanıcı adı (isteğe bağlı)", "com_auth_username_max_length": "Kullanıcı adı en fazla 20 karakter olmalıdır", "com_auth_username_min_length": "Kullanıcı adı en az 2 karakter olmalıdır", "com_auth_welcome_back": "<PERSON><PERSON><PERSON><PERSON>", "com_click_to_download": "(indirmek için tı<PERSON>ın)", "com_download_expired": "(indirme süresi doldu)", "com_download_expires": "(indirmek iç<PERSON> tı<PERSON>n - {{0}} tari<PERSON>de sona eriyor)", "com_endpoint": "Uç Nokta", "com_endpoint_agent": "<PERSON><PERSON>", "com_endpoint_agent_model": "<PERSON><PERSON> (Önerilen: GPT-3.5)", "com_endpoint_agent_placeholder": "Lütfen bir A<PERSON>", "com_endpoint_ai": "<PERSON><PERSON><PERSON>", "com_endpoint_anthropic_maxoutputtokens": "Yanıttaki maksimum token sayısı. Daha kısa yanıtlar için düşük bir değer, daha uzun yanıtlar için yüksek bir değer belirtin.", "com_endpoint_anthropic_prompt_cache": "İstem önbelleğe alma, API çağrıları arasında büyük bağlam veya talimatların yeniden kullanılmasına izin vererek maliyetleri ve gecikmeyi azaltır", "com_endpoint_anthropic_temp": "0 ile 1 arasında değişir. Analitik / çoktan seçmeli sorular için 0'a yakın, yaratıcı ve üretken görevler için 1'e yakın bir sıcaklık kullanın. Bu parametre ile Olasılık Kütüphanesini değiştirmeyi öneririz (ikisini birden değiştirmemek).", "com_endpoint_anthropic_topk": "Top-k, modelin çıktı için token seçimini nasıl yaptığını değiştirir. 1 olan bir top-k, modelin kelime haznesindeki en olası tokenin seçildiği (açgözlü kod çözme olarak da adlandırılır) anlamına gelirken, 3 olan bir top-k, bir sonraki tokenin en olası üç token arasından (sıcaklık kullanılarak) seçileceği anlamına gelir.", "com_endpoint_anthropic_topp": "Modelin çıktı için token seçim şeklini değiştirir. Tokenlar, en olasılıktan (bkz. topK parametresi) en az olasıya kadar seçilir ve olasılıkları toplamı, top-p değerine eşit olana kadar devam eder.", "com_endpoint_assistant": "Asistan", "com_endpoint_assistant_model": "Asistan Modeli", "com_endpoint_assistant_placeholder": "Lütfen sağ tarafta bir Asistan seçin", "com_endpoint_completion": "<PERSON><PERSON><PERSON><PERSON>", "com_endpoint_completion_model": "<PERSON><PERSON>lan<PERSON>i (Önerilen: GPT-4)", "com_endpoint_config_click_here": "Buraya Tıklayın", "com_endpoint_config_google_api_info": "Gemini için Yapay Zeka Dil API Anahtarınızı almak için,", "com_endpoint_config_google_api_key": "Google API Anahtarı", "com_endpoint_config_google_cloud_platform": "(Google Cloud Platform'dan)", "com_endpoint_config_google_gemini_api": "(Gemini API)", "com_endpoint_config_google_service_key": "Google Hizmet Hesabı Anahtarı", "com_endpoint_config_key": "API Anahtarını Ayarla", "com_endpoint_config_key_encryption": "Anahtarı<PERSON><PERSON><PERSON> ve şu tarihte silinir", "com_endpoint_config_key_for": "API Anahtarını Ayarla", "com_endpoint_config_key_google_need_to": "Şu işlemi yapmanız gerekiyor", "com_endpoint_config_key_google_service_account": "Bir Hizmet Hesabı oluşturun", "com_endpoint_config_key_google_vertex_ai": "Vertex AI'ı Etkinleştirin", "com_endpoint_config_key_google_vertex_api": "Google Cloud'da API'yi <PERSON>, a<PERSON><PERSON><PERSON><PERSON> ", "com_endpoint_config_key_google_vertex_api_role": "'Oluştur ve Devam Et' seçeneğine tıkladığınızdan emin olun ve en azından 'Vertex AI Kullanıcı' rolünü verin. <PERSON>, burada karşıya yüklemek için bir JSON anahtarı oluşturun.", "com_endpoint_config_key_import_json_key": "Hizmet Hesabı JSON Anahtarını İçe Aktar.", "com_endpoint_config_key_import_json_key_invalid": "Geçersiz Hizmet Hesabı JSON Anahtarı, do<PERSON>ru dosyayı karşıya yüktediniz mi?", "com_endpoint_config_key_import_json_key_success": "Hizmet Hesabı JSON Anahtarı Başarıyla Karşıya Yüklendi", "com_endpoint_config_key_name": "<PERSON><PERSON><PERSON>", "com_endpoint_config_key_never_expires": "Ana<PERSON>r<PERSON><PERSON><PERSON><PERSON> asla sona er<PERSON>k", "com_endpoint_config_placeholder": "Sohbet etmek için Anahtarınızı Başlık menüsünde ayarlayın.", "com_endpoint_config_value": "<PERSON><PERSON><PERSON> giri<PERSON>", "com_endpoint_context": "Bağlam", "com_endpoint_context_info": "Bağlam için kullanılabilecek maksimum token sayısı. B<PERSON>, her istek için kaç token gönderileceğini kontrol etmek içindir. Belirtilmezse, bilinen modellerin bağlam boyutuna göre sistem varsayılanları kullanılacaktır. Daha yüksek değerler belirlemek hatalara ve/veya daha yüksek token maliyetine neden olabilir.", "com_endpoint_context_tokens": "<PERSON><PERSON><PERSON>um Bağlam Tokenleri", "com_endpoint_custom_name": "<PERSON><PERSON>", "com_endpoint_default": "varsay<PERSON>lan", "com_endpoint_default_blank": "varsayılan: boş", "com_endpoint_default_empty": "varsayılan: boş", "com_endpoint_default_with_num": "varsayılan: {{0}}", "com_endpoint_examples": "<PERSON><PERSON><PERSON><PERSON>", "com_endpoint_export": "Dışa Aktar", "com_endpoint_export_share": "Dışa Aktar / Paylaş", "com_endpoint_frequency_penalty": "Sıklık Cezası", "com_endpoint_func_hover": "Eklentilerin OpenAI Fonksiyonları olarak kullanılmasını etkinleştir", "com_endpoint_google_custom_name_placeholder": "Google için özel bir ad ayarlayın", "com_endpoint_google_maxoutputtokens": "Yanıttaki maksimum token sayısı. Daha kısa yanıtlar için düşük bir değer, daha uzun yanıtlar için yüksek bir değer belirtin.", "com_endpoint_google_temp": "<PERSON><PERSON><PERSON><PERSON> de<PERSON> = da<PERSON> r<PERSON>, d<PERSON><PERSON><PERSON><PERSON> de<PERSON> = daha odaklı ve belirleyici. Bu parametre ile Olasılık Kütüphanesini değiştirmeyi öneririz (ikisini birden değiştirmemek).", "com_endpoint_google_topk": "Top-k, modelin çıktı için token seçimini nasıl yaptığını değiştirir. 1 olan bir top-k, modelin kelime haznesindeki en olası tokenin seçildiği (açgözlü kod çözme olarak da adlandırılır) anlamına gelirken, 3 olan bir top-k, bir sonraki tokenin en olası üç token arasından (sıcaklık kullanılarak) seçileceği anlamına gelir.", "com_endpoint_google_topp": "<PERSON>las<PERSON><PERSON><PERSON><PERSON>, modelin çıktı için token seçme şeklini değiştirir. <PERSON><PERSON><PERSON>, en olasılıktan (bkz. topK parametresi) en az olasıya kadar seçilir ve olasılıkları toplamı, top-p değ<PERSON>ne eşit olana kadar devam eder.", "com_endpoint_instructions_assistants": "Talimatları Geçersiz Kıl", "com_endpoint_instructions_assistants_placeholder": "Asistanın talimatlarını geçersiz kılar. Bu, davranışı tek tek çalışma bazında değiştirmek için yararlıdır.", "com_endpoint_max_output_tokens": "<PERSON><PERSON><PERSON>um Çıktı Tokenleri", "com_endpoint_message": "<PERSON><PERSON>", "com_endpoint_message_new": "<PERSON><PERSON> {{0}}", "com_endpoint_message_not_appendable": "Mesajınızı düzenleyin veya yeniden oluşturun.", "com_endpoint_my_preset": "<PERSON><PERSON>", "com_endpoint_no_presets": "<PERSON><PERSON><PERSON><PERSON> hazır ayar yok, bi<PERSON> oluşturmak için ayar düğ<PERSON>ini kullanın", "com_endpoint_open_menu": "Menüyü Aç", "com_endpoint_openai_custom_name_placeholder": "AI için özel bir ad ayarlayın", "com_endpoint_openai_detail": "Görsel istekleri iç<PERSON>ü<PERSON>ük. \"Düşük\" daha ucuz ve hızlıdır, \"Yüksek\" daha detaylı ve pahalıdır, \"Otomatik\" ise görüntü çözünürlüğüne göre ikisi arasında otomatik olarak bir seçim yapar.", "com_endpoint_openai_freq": " -2.0 ile 2.0 arasında bir değer. <PERSON><PERSON><PERSON><PERSON>, metinde daha önceki sıklığa bağlı olarak yeni tokenları cezalandırır, bu da <PERSON>in aynı hattı kelimesi kelimesine tekrar etme olasılığını azaltır.", "com_endpoint_openai_max": "Üretilecek maksimum token sayısı. G<PERSON>ş <PERSON>ının ve üretilen tokenların toplam uzunluğu modelin bağlam uzunluğu ile sınırlıdır.", "com_endpoint_openai_max_tokens": "<PERSON>steğ<PERSON> bağlı `max_tokens` al<PERSON><PERSON>, sohbet tamamlamalarında üretilebilecek maksimum token sayısını temsil eder. G<PERSON>ş tokenlarının ve üretilen tokenların toplam uzunluğu, modellerin bağlam uzunluğu ile sınırlıdır. Bu sayının maksimum bağlam tokenlarını aşması durumunda hatalarla karşılaşabilirsiniz.", "com_endpoint_openai_pres": " -2.0 ile 2.0 arasında bir değer. <PERSON><PERSON><PERSON><PERSON>, metinde daha önceki varlıklarına dayalı olarak yeni tokenları cezalandırır, bu da modelin yeni konular hakkında konuşma olasılığını artırır.", "com_endpoint_openai_prompt_prefix_placeholder": "Sistem Mesajına dahil edilecek özel talimatlar ayarlayın. Varsayılan: yok", "com_endpoint_openai_reasoning_effort": "Sadece o1 modelleri: akıl yürütme modelleri için akıl yürütme çabasını kısıtlar. Akıl yürütme çabasını azaltmak, daha hızlı yanıtlara ve yanıtta akıl yürütmeye daha az token kullanılmasına neden olabilir.", "com_endpoint_openai_resend": "Daha önce eklenmiş tüm görüntüleri yeniden gönderin. Not: Bu, token maliyetinizi önemli ölçüde artırabilir ve birden çok görüntü eklenmişse hatalarla karşılaşabilirsiniz.", "com_endpoint_openai_resend_files": "Daha önce eklenmiş tüm dosyaları yeniden gönderin. Not: Bu, token maliyetinizi artıracaktır ve birden çok eklenmiş dosya ile hatalarla karşılaşabilirsiniz.", "com_endpoint_openai_stop": "API'nin ek tokenlar üretmeyi durduracağı en fazla 4 sıra.", "com_endpoint_openai_temp": "<PERSON><PERSON><PERSON><PERSON> de<PERSON> = daha r<PERSON>, d<PERSON><PERSON><PERSON><PERSON> = daha odaklı ve belirleyici. Bu parametre ile Olasılık Kütüphanesi (top-p) değiştirmeyi öneririz (ikisini birden değiştirmemek).", "com_endpoint_openai_topp": "Sıcaklıkla örneklemenin bir alternatifi olan, çekirdek örnekleme olarak adlandırılır, model top_p olasılık kütlesine sahip tokenların sonuçlarını dikkate alır. <PERSON>i 0.1, sadece top 10% olasılık kütlesine sahip tokenların dikkate alındığı anlamına gelir. Sıcaklık (temperature) ile değil bu parametre ile değiştirmenizi öneririz.", "com_endpoint_output": "Çıktı", "com_endpoint_plug_image_detail": "Görüntü <PERSON>", "com_endpoint_plug_resend_files": "Dosyaları Yeniden Gönderin", "com_endpoint_plug_set_custom_instructions_for_gpt_placeholder": "Sistem Mesajına dahil edilecek özel talimatlar ayarlayın. Varsayılan: yok", "com_endpoint_plug_skip_completion": "Tamamlamayı Atla", "com_endpoint_plug_use_functions": "Eklenti Fonksiyonlarını Kullanın", "com_endpoint_presence_penalty": "Varlık Cezası", "com_endpoint_preset": "<PERSON><PERSON>ır ayar", "com_endpoint_preset_default": "<PERSON>u anda varsayılan hazır ayar.", "com_endpoint_preset_default_item": "Varsayılan:", "com_endpoint_preset_default_none": "<PERSON><PERSON><PERSON> hazır ayar yok.", "com_endpoint_preset_default_removed": "Art<PERSON>k <PERSON>ılan hazır ayar değil.", "com_endpoint_preset_delete_confirm": "Bu hazır ayarı silmek istediğinizden emin misiniz?", "com_endpoint_preset_delete_error": "Hazır ayarınızı silerken bir hata oluştu. Lütfen tekrar deneyin.", "com_endpoint_preset_import": "Hazır Ayar İthal Edildi!", "com_endpoint_preset_import_error": "Ha<PERSON>ır ayarınızı karşıya yüklerken bir hata oluştu. Lütfen tekrar deneyin.", "com_endpoint_preset_name": "<PERSON><PERSON><PERSON><PERSON>", "com_endpoint_preset_save_error": "Ha<PERSON>ır ayarınızı kaydederken bir hata oluştu. Lütfen tekrar deneyin.", "com_endpoint_preset_selected": "Hazır Ayar Aktif!", "com_endpoint_preset_selected_title": "Aktif!", "com_endpoint_preset_title": "<PERSON><PERSON><PERSON><PERSON>", "com_endpoint_presets": "<PERSON><PERSON>ı<PERSON>", "com_endpoint_presets_clear_warning": "Tüm hazır ayarları temizlemek istediğinizden emin misiniz? Bu geri alınamaz.", "com_endpoint_prompt_cache": "İstem Önbelleğini <PERSON>", "com_endpoint_prompt_prefix": "<PERSON><PERSON>", "com_endpoint_prompt_prefix_assistants": "Ek <PERSON>", "com_endpoint_prompt_prefix_assistants_placeholder": "Asistanın ana talimatlarının üzerine ek talimatlar veya bağlam ekleyin. Boşsa yok sayılır.", "com_endpoint_prompt_prefix_placeholder": "Özel talimatlar veya bağlam ayarlayın. Boşsa yok sayılır.", "com_endpoint_reasoning_effort": "Akıl Yürütme Çabası", "com_endpoint_save_as_preset": "<PERSON><PERSON><PERSON><PERSON>", "com_endpoint_search": "Uç noktayı ada göre ara", "com_endpoint_set_custom_name": "Özelleştirm<PERSON><PERSON>, bö<PERSON>ce bu ayarlanabilir", "com_endpoint_skip_hover": "Tamamlama adımını atlamayı etkinleştir, bu adım nihai yanıtı ve oluşturulan adımları gözden geçirir", "com_endpoint_stop": "Durdurma Sıraları", "com_endpoint_stop_placeholder": "Değerleri ayırmak için `Enter` tuşuna basın", "com_endpoint_temperature": "Sıcaklık", "com_endpoint_top_k": "Top K", "com_endpoint_top_p": "Top P", "com_endpoint_use_active_assistant": "Etkin Asistanı Kullan", "com_error_expired_user_key": "Belirtilen {{0}} anahtarı {{1}} tarihinde süresi dolmuş. Lütfen bir anahtar sağlayın ve tekrar deneyin.", "com_error_files_dupe": "<PERSON><PERSON>nen dosya tespit edildi.", "com_error_files_empty": "Boş dosyalara izin verilmez.", "com_error_files_process": "<PERSON><PERSON>a <PERSON>irk<PERSON> bir hata o<PERSON>.", "com_error_files_unsupported_capability": "<PERSON>u dosya türünü destekleyen hiçbir yetenek etkin değil.", "com_error_files_upload": "<PERSON><PERSON><PERSON> bir hata <PERSON>.", "com_error_files_upload_canceled": "<PERSON><PERSON><PERSON> yü<PERSON>me isteği iptal edildi. Not: dosya yüklemesi hala işleniyor olabilir ve manuel olarak silinmesi gerekecektir.", "com_error_files_validation": "<PERSON><PERSON><PERSON>ırken bir hata oluştu.", "com_error_input_length": "Son mesajın token sayıs<PERSON> çok uzun, token limitini aşıyor ({{0}}). Lütfen mesajınızı kısaltın, konuşma parametrelerinden maksimum bağlam boyutunu ayarlayın veya devam etmek için konuşmayı çatallayın.", "com_error_invalid_user_key": "Sağlanan anahtar geçersiz. Lütfen bir anahtar sağlayın ve tekrar deneyin.", "com_error_moderation": "Gönderdiğiniz içerik, topluluk kurallarımıza uymadığı için moderasyon sistemimiz tarafından işaretlenmiş görünüyor. Bu belirli konu ile devam edemiyoruz. Başka sorularınız veya incelemek istediğiniz başka konular varsa, mesajınızı düzenleyin veya yeni bir konuşma başlatın.", "com_error_no_base_url": "Temel URL bulunamadı. Lütfen bir tane sağlayın ve tekrar deneyin.", "com_error_no_user_key": "<PERSON><PERSON><PERSON> bulu<PERSON>adı. Lütfen bir anahtar sağlayın ve tekrar deneyin.", "com_files_filter": "Dosyaları filtrele...", "com_files_no_results": "<PERSON><PERSON><PERSON> bulunamadı.", "com_files_number_selected": "{{0}} dosya/dosyadan {{1}} seçildi", "com_generated_files": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>:", "com_hide_examples": "Örnekleri Gizle", "com_nav_account_settings": "<PERSON><PERSON><PERSON>", "com_nav_always_make_prod": "Her zaman yeni sürümleri üretime al", "com_nav_archive_created_at": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_archive_name": "Ad", "com_nav_archived_chats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_at_command": "@-<PERSON><PERSON><PERSON>", "com_nav_at_command_description": "<PERSON><PERSON> no<PERSON>, <PERSON><PERSON><PERSON>, ön ayarları vb. değiştirmek için \"@\" komutunu aç/kapat", "com_nav_audio_play_error": "<PERSON>s oynatma hatası: {{0}}", "com_nav_audio_process_error": "<PERSON><PERSON> i<PERSON>lem<PERSON> hatası: {{0}}", "com_nav_auto_scroll": "<PERSON><PERSON><PERSON> açıldığında otomatik olarak son mesaja kaydır", "com_nav_auto_send_prompts": "İstemleri Otomatik Gönder", "com_nav_auto_send_text": "<PERSON><PERSON> o<PERSON>ati<PERSON> (3 sn sonra)", "com_nav_auto_send_text_disabled": "devre dışı bırakmak için -1 ayarlayın", "com_nav_auto_transcribe_audio": "Sesi otomatik olarak yazıya dök", "com_nav_automatic_playback": "Son Mesajı Otomatik Çal (yalnızca dış)", "com_nav_balance": "<PERSON><PERSON>", "com_nav_browser": "Tarayıcı", "com_nav_change_picture": "<PERSON><PERSON><PERSON>", "com_nav_chat_commands": "So<PERSON>bet Komutları", "com_nav_chat_commands_info": "<PERSON><PERSON> komut<PERSON>, mesa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> başına belirli karakterler yazarak etkinleştirilir. Her komut, belirlenen öneki ile tetiklenir. Bu karakterleri sıklıkla mesaj başlatmak için kullanıyorsanız bunları devre dışı bırakabilirsiniz.", "com_nav_chat_direction": "<PERSON><PERSON><PERSON>", "com_nav_clear_all_chats": "<PERSON><PERSON><PERSON> sohbetleri temizle", "com_nav_clear_cache_confirm_message": "Önbelleği temizlemek istediğinizden emin misiniz?", "com_nav_clear_conversation": "<PERSON><PERSON><PERSON><PERSON><PERSON> temizle", "com_nav_clear_conversation_confirm_message": "Tüm konuşmaları temizlemek istediğinizden emin misiniz? Bu işlem geri alınamaz.", "com_nav_close_sidebar": "<PERSON>i kapat", "com_nav_commands": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_confirm_clear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_conversation_mode": "<PERSON><PERSON>ş<PERSON>", "com_nav_convo_menu_options": "Konuşma Menü Seçenekleri", "com_nav_db_sensitivity": "Desibel hassasiyeti", "com_nav_delete_account": "Hesabı sil", "com_nav_delete_account_button": "Hesabımı kalıcı olarak sil", "com_nav_delete_account_confirm": "<PERSON><PERSON>bı silmek istediğinizden emin misiniz?", "com_nav_delete_account_email_placeholder": "Lütfen hesap e-postanızı girin", "com_nav_delete_cache_storage": "Önbellek depolamayı sil", "com_nav_delete_data_info": "Tüm verileriniz silinecektir.", "com_nav_delete_warning": "UYARI: Bu işlem hesabınızı kalıcı olarak silecektir.", "com_nav_enable_cache_tts": "TTS önbelleğini etkinleştir", "com_nav_enable_cloud_browser_voice": "Bulut tabanlı sesleri kullan", "com_nav_enabled": "<PERSON><PERSON><PERSON>", "com_nav_engine": "Motor", "com_nav_enter_to_send": "Mesajları göndermek için Enter tuşuna basın", "com_nav_export": "Dışa aktar", "com_nav_export_all_message_branches": "<PERSON>ü<PERSON> mesaj da<PERSON>ı<PERSON>ı dışa aktar", "com_nav_export_conversation": "Konuşmayı dışa aktar", "com_nav_export_filename": "<PERSON><PERSON><PERSON> adı", "com_nav_export_filename_placeholder": "<PERSON><PERSON><PERSON> adın<PERSON> belirle", "com_nav_export_include_endpoint_options": "Uç nokta seçeneklerini dahil et", "com_nav_export_recursive": "<PERSON><PERSON><PERSON>", "com_nav_export_recursive_or_sequential": "Yinelenen mi, ardışık mı?", "com_nav_export_type": "<PERSON><PERSON><PERSON>", "com_nav_external": "<PERSON><PERSON>", "com_nav_font_size": "Yazı Boyutu", "com_nav_font_size_base": "Orta", "com_nav_font_size_lg": "Büyük", "com_nav_font_size_sm": "Küçük", "com_nav_font_size_xl": "Çok Büyük", "com_nav_font_size_xs": "Çok Küçük", "com_nav_help_faq": "Yardım & SS", "com_nav_hide_panel": "Sağdaki paneli gizle", "com_nav_info_code_artifacts": "Sohbet yanında deneysel kod yapıtlarının görüntülenmesini etkinleştirir", "com_nav_info_custom_prompt_mode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, var<PERSON><PERSON>lan yapıtlar sistem istemi dahil edilmeyecektir. Bu modda tüm yapıt oluşturma talimatları manuel olarak sağlanmalıdır.", "com_nav_info_enter_to_send": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, `ENTER` tuşuna basmak mesajınızı gönderecektir. <PERSON>re dışı bırakıldığı<PERSON>, Enter tuşuna basmak yeni bir satır ekleyecek ve mesajınızı göndermek için `CTRL + ENTER` / `⌘ + ENTER` tuşlarına basmanız gerekecektir.", "com_nav_info_fork_change_default": "`<PERSON><PERSON><PERSON> g<PERSON>ür mesajlar` yalnızca seçili mesaja giden doğrudan yolu içerir. `<PERSON><PERSON><PERSON><PERSON> dalları dahil et` yol boyunca dalları ekler. `Buradan/buraya tümünü dahil et` tüm bağlantılı mesajları ve dalları içerir.", "com_nav_info_fork_split_target_setting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, se<PERSON><PERSON><PERSON> göre hedef mesajdan konuşmada<PERSON> en son mesaja kadar başlayacaktır.", "com_nav_info_include_shadcnui": "Etkinleş<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, shadcn/ui bileşenlerini kullanma talimatları dahil edilecektir. shadcn/ui, Radix UI ve Tailwind CSS kullanılarak oluşturulmuş yeniden kullanılabilir bileşenler koleksiyonudur. Not: bunlar uzun talimatlardır, yalnızca LLM'ye doğru içe aktarmaları ve bileşenleri bildirmek sizin için önemliyse etkinleştirmelisiniz. Bu bileşenler hakkında daha fazla bilgi için: https://ui.shadcn.com/", "com_nav_info_latex_parsing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, mesajlardaki LaTeX kodu matematiksel denklemler olarak işlenecektir. LaTeX işlemeye ihtiyacınız yoksa performansı artırmak için bunu devre dışı bırakabilirsiniz.", "com_nav_info_save_draft": "<PERSON>tkinleş<PERSON><PERSON><PERSON>ğ<PERSON><PERSON>, sohbet formuna girdiğiniz metin ve ekler otomatik olarak yerel olarak taslak olarak kaydedilecektir. <PERSON><PERSON> taslaklar, sayfayı yeniden yüklediğinizde veya farklı bir konuşmaya geçtiğinizde bile mevcut olacaktır. Taslaklar cihazınızda yerel olarak depolanır ve mesaj gönderildikten sonra silinir.", "com_nav_info_show_thinking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sohbet düşünme açılır menülerini varsayılan olarak açık gösterecek, yapay zekanın akıl yürütmesini gerçek zamanlı olarak görmenize olanak tanıyacaktır. Devre dışı bırakıldığında, daha temiz ve düzenli bir arayüz için düşünme açılır menüleri varsayılan olarak kapalı kalacaktır", "com_nav_info_user_name_display": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, g<PERSON>nderenin kullanıcı adı gönder<PERSON>ğ<PERSON>z her mesajın üzerinde gösterilecektir. Devre dışı bırak<PERSON>ldığı<PERSON>, mesajlarınızın üzerinde sadece \"Siz\" göreceksiniz.", "com_nav_lang_arabic": "العربية", "com_nav_lang_auto": "Auto detect", "com_nav_lang_brazilian_portuguese": "Portuguê<PERSON>", "com_nav_lang_chinese": "中文", "com_nav_lang_dutch": "Nederlands", "com_nav_lang_english": "English", "com_nav_lang_estonian": "<PERSON><PERSON><PERSON> keel", "com_nav_lang_finnish": "<PERSON><PERSON>", "com_nav_lang_french": "Français ", "com_nav_lang_georgian": "ქართული", "com_nav_lang_german": "De<PERSON>ch", "com_nav_lang_hebrew": "עברית", "com_nav_lang_indonesia": "Indonesia", "com_nav_lang_italian": "Italiano", "com_nav_lang_japanese": "日本語", "com_nav_lang_korean": "한국어", "com_nav_lang_polish": "<PERSON><PERSON>", "com_nav_lang_portuguese": "Português", "com_nav_lang_russian": "Русский", "com_nav_lang_spanish": "Español", "com_nav_lang_swedish": "Svenska", "com_nav_lang_thai": "ไทย", "com_nav_lang_traditional_chinese": "繁體中文", "com_nav_lang_turkish": "Türkçe", "com_nav_lang_vietnamese": "Tiếng <PERSON>", "com_nav_language": "Dil", "com_nav_latex_parsing": "Mesajlarda LaTeX işleme (performansı etkileyebilir)", "com_nav_log_out": "Çıkış yap", "com_nav_long_audio_warning": "Daha uzun metinlerin işlenmesi daha uzun sürecektir.", "com_nav_maximize_chat_space": "Sohbet alanını maksimize et", "com_nav_modular_chat": "Konuşmalar arasında uç noktaları değiştir", "com_nav_my_files": "Dosyalarım", "com_nav_not_supported": "Desteklenmiyor", "com_nav_open_sidebar": "Yan paneli aç", "com_nav_playback_rate": "Ses Çalma Hızı", "com_nav_plugin_auth_error": "Bu eklenti ile kimlik doğrulama işlemi sırasında bir hata oluştu. Lütfen tekrar deneyin.", "com_nav_plugin_install": "<PERSON><PERSON><PERSON>", "com_nav_plugin_search": "Eklentileri Ara", "com_nav_plugin_store": "Eklenti mağazası", "com_nav_plugin_uninstall": "Kaldır", "com_nav_plus_command": "+-<PERSON><PERSON><PERSON>", "com_nav_plus_command_description": "Çoklu yanıt ayarı eklemek için \"+\" komutunu aç/kapat", "com_nav_profile_picture": "<PERSON><PERSON>", "com_nav_save_drafts": "Taslakları yerel olarak kaydet", "com_nav_scroll_button": "<PERSON><PERSON> ka<PERSON>ı<PERSON>", "com_nav_search_placeholder": "Mesajları ara", "com_nav_send_message": "<PERSON><PERSON><PERSON>", "com_nav_setting_account": "<PERSON><PERSON><PERSON>", "com_nav_setting_beta": "Beta özellikleri", "com_nav_setting_chat": "<PERSON><PERSON><PERSON>", "com_nav_setting_data": "<PERSON><PERSON> k<PERSON>", "com_nav_setting_general": "<PERSON><PERSON>", "com_nav_setting_speech": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_settings": "<PERSON><PERSON><PERSON>", "com_nav_shared_links": "Paylaşılan bağlantılar", "com_nav_show_code": "<PERSON><PERSON> yo<PERSON><PERSON><PERSON><PERSON><PERSON> kull<PERSON>ırken her zaman kodu göster", "com_nav_show_thinking": "Düşünme Açılır Menülerini Varsayılan Olarak Aç", "com_nav_slash_command": "/-<PERSON><PERSON><PERSON>", "com_nav_slash_command_description": "Klavye ile istem seçmek için \"/\" komutunu aç/kapat", "com_nav_speech_to_text": "<PERSON><PERSON>", "com_nav_stop_generating": "<PERSON><PERSON><PERSON><PERSON> du<PERSON>", "com_nav_text_to_speech": "<PERSON><PERSON>", "com_nav_theme": "<PERSON><PERSON>", "com_nav_theme_dark": "Karanlık", "com_nav_theme_light": "Aydınlık", "com_nav_theme_system": "Sistem", "com_nav_tool_dialog": "Asistan Araçları", "com_nav_tool_dialog_agents": "<PERSON><PERSON>", "com_nav_tool_dialog_description": "Araç seçimlerinin kalıcı olması için asistan kaydedilmelidir.", "com_nav_tool_remove": "Kaldır", "com_nav_tool_search": "Araçları Ara", "com_nav_user": "KULLANICI", "com_nav_user_msg_markdown": "Kullanıcı mesajlarını markdown olarak işle", "com_nav_user_name_display": "Mesajlarda kullanıcı adını görüntüle", "com_nav_voice_select": "<PERSON><PERSON>", "com_show_agent_settings": "<PERSON><PERSON>ö<PERSON>", "com_show_completion_settings": "Tamamlama Ayarlarını Göster", "com_show_examples": "Örnekleri Göster", "com_sidepanel_agent_builder": "<PERSON><PERSON>", "com_sidepanel_assistant_builder": "Asistan Yapıcı", "com_sidepanel_attach_files": "Dosyaları Ekle", "com_sidepanel_conversation_tags": "<PERSON><PERSON>", "com_sidepanel_hide_panel": "<PERSON><PERSON>", "com_sidepanel_manage_files": "Dosyaları Yönet", "com_sidepanel_parameters": "Parametreler", "com_ui_accept": "Kabul ediyorum", "com_ui_add": "<PERSON><PERSON>", "com_ui_add_model_preset": "Ek bir yanıt i<PERSON>in bir model veya ön ayar e<PERSON>in", "com_ui_add_multi_conversation": "Çoklu konuşma ekle", "com_ui_admin": "Yönetici", "com_ui_admin_access_warning": "Bu özelliğe Yönetici erişimini devre dışı bırakmak, yenileme gerektiren beklenmedik kullanıcı arayüzü sorunlarına neden olabilir. <PERSON><PERSON><PERSON><PERSON>, geri almanın tek yolu tüm rolleri etkileyen librechat.yaml yapılandırmasındaki arayüz ayarı aracılığıyladır.", "com_ui_admin_settings": "Yönetici Ayarları", "com_ui_advanced": "Gelişmiş", "com_ui_agent": "<PERSON><PERSON>", "com_ui_agent_delete_error": "<PERSON><PERSON> bir hata <PERSON>", "com_ui_agent_deleted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_agent_duplicate_error": "<PERSON><PERSON>ılırken bir hata oluş<PERSON>", "com_ui_agent_duplicated": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> çoğaltıldı", "com_ui_agent_editing_allowed": "<PERSON><PERSON><PERSON> k<PERSON>anıcılar zaten bu ajanı düzenleyebilir", "com_ui_agent_shared_to_all": "<PERSON>u ajan tüm kullanıcılarla paylaşıldı", "com_ui_agents": "<PERSON><PERSON><PERSON>", "com_ui_agents_allow_create": "<PERSON><PERSON>zin ver", "com_ui_agents_allow_share_global": "Ajanları tüm kullanıcılarla paylaşmaya izin ver", "com_ui_agents_allow_use": "<PERSON><PERSON> izin ver", "com_ui_all": "hepsi", "com_ui_all_proper": "Tümü", "com_ui_archive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_archive_error": "Konuşmayı arşivleyemedi", "com_ui_artifact_click": "Açmak için tı<PERSON>ın", "com_ui_artifacts": "<PERSON><PERSON><PERSON>tlar", "com_ui_artifacts_toggle": "Yapıtlar Arayüzünü Aç/Kapat", "com_ui_ascending": "<PERSON><PERSON>", "com_ui_assistant": "Asistan", "com_ui_assistant_delete_error": "Asistan silme sırasında bir hata oluştu", "com_ui_assistant_deleted": "Asistan ba<PERSON><PERSON><PERSON><PERSON>", "com_ui_assistants": "Asistanlar", "com_ui_assistants_output": "Asistan Çıkışı", "com_ui_attach_error": "Dosya eklenemiyor. Bir konuşma oluşturun veya seçin ya da sayfayı yenilemeyi deneyin.", "com_ui_attach_error_openai": "Asistan dosyalarını diğer uç noktalara ekleyemezsiniz", "com_ui_attach_error_size": "Uç nokta için dosya boyutu sınırı aşıldı:", "com_ui_attach_error_type": "Uç nokta için desteklenmeyen dosya türü:", "com_ui_attach_warn_endpoint": "Asistan olmayan dosyalar uyumlu bir araç olmadan göz ardı edilebilir", "com_ui_attachment": "Ek", "com_ui_authentication": "Kimlik Doğrulama", "com_ui_avatar": "Avatar", "com_ui_back_to_chat": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_back_to_prompts": "İstemlere Dön", "com_ui_bookmark_delete_confirm": "Bu yer imini silmek istediğinizden emin misiniz?", "com_ui_bookmarks": "<PERSON><PERSON>", "com_ui_bookmarks_add": "<PERSON><PERSON><PERSON>", "com_ui_bookmarks_add_to_conversation": "<PERSON><PERSON><PERSON> sohbete ekle", "com_ui_bookmarks_count": "<PERSON><PERSON>", "com_ui_bookmarks_create_error": "<PERSON>r imi olu<PERSON>ulu<PERSON>en bir hata olu<PERSON>", "com_ui_bookmarks_create_exists": "Bu yer imi zaten mevcut", "com_ui_bookmarks_create_success": "<PERSON>r imi ba<PERSON><PERSON><PERSON><PERSON> oluş<PERSON>uld<PERSON>", "com_ui_bookmarks_delete": "<PERSON><PERSON>", "com_ui_bookmarks_delete_error": "Yer imi si<PERSON>en bir hata <PERSON>", "com_ui_bookmarks_delete_success": "<PERSON>r imi ba<PERSON><PERSON><PERSON><PERSON> si<PERSON>i", "com_ui_bookmarks_description": "<PERSON><PERSON>ı<PERSON><PERSON>", "com_ui_bookmarks_edit": "<PERSON><PERSON>", "com_ui_bookmarks_filter": "Yer im<PERSON>ini filtrele...", "com_ui_bookmarks_new": "<PERSON><PERSON>", "com_ui_bookmarks_title": "Başlık", "com_ui_bookmarks_update_error": "Yer imi günce<PERSON>en bir hata o<PERSON>", "com_ui_bookmarks_update_success": "<PERSON>r imi ba<PERSON><PERSON><PERSON><PERSON>", "com_ui_bulk_delete_error": "Paylaşılan bağlantılar silinemedi", "com_ui_cancel": "İptal", "com_ui_chat": "<PERSON><PERSON><PERSON>", "com_ui_chat_history": "So<PERSON>bet Geçmişi", "com_ui_clear": "<PERSON><PERSON><PERSON>", "com_ui_clear_all": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> temizle", "com_ui_close": "Ka<PERSON><PERSON>", "com_ui_close_menu": "Men<PERSON><PERSON>ü <PERSON>", "com_ui_code": "Kod", "com_ui_collapse_chat": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_command_placeholder": "İsteğe bağlı: İstem için bir komut girin veya ad kullanılacak", "com_ui_command_usage_placeholder": "Bir İstemi komut veya ada göre se<PERSON>in", "com_ui_confirm_action": "<PERSON><PERSON><PERSON>", "com_ui_confirm_admin_use_change": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, siz dahil yöneticilerin erişimini engelleyecektir. <PERSON><PERSON> etmek istediğinizden emin misiniz?", "com_ui_confirm_change": "Değişikliği Onayla", "com_ui_context": "Bağlam", "com_ui_continue": "<PERSON><PERSON> et", "com_ui_controls": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_copied": "Kopyalandı!", "com_ui_copied_to_clipboard": "Panoya kopyalandı", "com_ui_copy_code": "Kodu k<PERSON>ala", "com_ui_copy_link": "Bağlantıyı kopyala", "com_ui_copy_to_clipboard": "<PERSON><PERSON> k<PERSON>", "com_ui_create": "Oluştur", "com_ui_create_link": "Bağlantı oluştur", "com_ui_create_prompt": "İstem Oluştur", "com_ui_currently_production": "<PERSON><PERSON> anda <PERSON>", "com_ui_custom_prompt_mode": "Özel İstem Modu", "com_ui_dashboard": "Gösterge Paneli", "com_ui_date": "<PERSON><PERSON><PERSON>", "com_ui_date_april": "<PERSON><PERSON>", "com_ui_date_august": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_date_december": "Aralık", "com_ui_date_february": "Ş<PERSON><PERSON>", "com_ui_date_january": "Ocak", "com_ui_date_july": "Temmuz", "com_ui_date_june": "Haziran", "com_ui_date_march": "Mart", "com_ui_date_may": "<PERSON><PERSON><PERSON>", "com_ui_date_november": "Kasım", "com_ui_date_october": "<PERSON><PERSON>", "com_ui_date_previous_30_days": "Önceki 30 gün", "com_ui_date_previous_7_days": "Önceki 7 gün", "com_ui_date_september": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_date_today": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_date_yesterday": "<PERSON><PERSON><PERSON>", "com_ui_decline": "Kabul etmiyorum", "com_ui_delete": "Sil", "com_ui_delete_action": "<PERSON><PERSON><PERSON>", "com_ui_delete_action_confirm": "<PERSON>u e<PERSON>mi silmek istediğinizden emin misiniz?", "com_ui_delete_agent_confirm": "Bu ajanı silmek istediğinizden emin misiniz?", "com_ui_delete_assistant_confirm": "Bu Asistanı gerçekten silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.", "com_ui_delete_confirm": "<PERSON><PERSON>", "com_ui_delete_confirm_prompt_version_var": "<PERSON><PERSON>, \"{{0}}\" iç<PERSON> seçilen sürümü silecektir. Başka sürüm yoksa, istem silinecektir.", "com_ui_delete_conversation": "<PERSON><PERSON><PERSON>i sil?", "com_ui_delete_prompt": "İstem Silinsin mi?", "com_ui_delete_shared_link": "Paylaşılan bağlantı silinsin mi?", "com_ui_delete_tool": "Aracı Sil", "com_ui_delete_tool_confirm": "Bu aracı silmek istediğinizden emin misiniz?", "com_ui_descending": "<PERSON><PERSON><PERSON>", "com_ui_description": "<PERSON><PERSON>ı<PERSON><PERSON>", "com_ui_description_placeholder": "İsteğe bağlı: İstem için görüntülenecek bir açıklama girin", "com_ui_download": "<PERSON><PERSON><PERSON>", "com_ui_download_artifact": "Yapıtı İndir", "com_ui_download_error": "<PERSON><PERSON>a indirme hatası. <PERSON><PERSON>a silinmiş olabilir.", "com_ui_drag_drop": "Sürükle ve bırak", "com_ui_dropdown_variables": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ğ<PERSON>şkenleri:", "com_ui_dropdown_variables_info": "İstemleriniz için <PERSON> açılır menü<PERSON> oluşturun: `{{variable_name:option1|option2|option3}}`", "com_ui_duplicate": "Çoğalt", "com_ui_duplication_error": "Konuşma çoğaltılırken bir hata oluştu", "com_ui_duplication_processing": "Konuşma çoğaltılıyor...", "com_ui_duplication_success": "Konuşma başarıyla çoğaltıldı", "com_ui_edit": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_endpoint": "Uç Nokta", "com_ui_endpoint_menu": "LLM Uç Nokta Menüsü", "com_ui_enter": "Gir", "com_ui_enter_api_key": "API Anahtarı Girin", "com_ui_enter_openapi_schema": "OpenAPI şemanızı buraya girin", "com_ui_error": "<PERSON><PERSON>", "com_ui_error_connection": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON><PERSON> hata <PERSON>, say<PERSON><PERSON><PERSON> ye<PERSON><PERSON><PERSON> den<PERSON>.", "com_ui_error_save_admin_settings": "Yönetici ayarlarınız kaydedilirken bir hata oluştu.", "com_ui_examples": "Örnekler", "com_ui_export_convo_modal": "Konuşma Dışa Aktarma Modalı", "com_ui_field_required": "<PERSON><PERSON> alan g<PERSON>", "com_ui_filter_prompts": "İstemleri Filtrele", "com_ui_filter_prompts_name": "İstemleri ada göre filtrele", "com_ui_fork": "Çatallaş", "com_ui_fork_all_target": "<PERSON><PERSON><PERSON> tüm dahil et", "com_ui_fork_branches": "<PERSON>l<PERSON><PERSON> dalları dahil et", "com_ui_fork_change_default": "Varsayılan çatallama seçeneği", "com_ui_fork_default": "Varsayılan çatallama seçeneğini kullan", "com_ui_fork_error": "Konuşma çatallanmasında bir hata oluş<PERSON>", "com_ui_fork_from_message": "Bir çatallama seçeneği seç", "com_ui_fork_info_1": "Mesajların istenilen davranışla çatallanması için bu ayarı kullanın.", "com_ui_fork_info_2": "\"Ç<PERSON><PERSON>şma\", mevcut konuşmadaki belirli mesajlardan veya mesajlardan başlayan/sonlanan yeni bir konuşma oluşturmayı ifade eder, seç<PERSON>n seçeneklere göre bir kopya oluşturur.", "com_ui_fork_info_3": "\"Hede<PERSON> mesaj\" bu açı<PERSON><PERSON><PERSON> pencerenin açıldığı mesajı veya \"{{0}}\" iş<PERSON>lenirse, konuşmadaki son mesajı ifade eder.", "com_ui_fork_info_branches": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, görünen mesajları ilgili dallarla birlikte çatallandırır; di<PERSON><PERSON> bi<PERSON>, hedef mesaja giden doğrudan yolu ve yol üzerindeki dalları içerir.", "com_ui_fork_info_remember": "Çatallama işlemlerinde hızlıca tercih ettiğiniz seçenekleri hatırlayın, böylece gelecekte çatallama işlemlerini daha hızlı yapabilirsiniz.", "com_ui_fork_info_start": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, yuka<PERSON><PERSON><PERSON> seçilen davranı<PERSON>a göre bu mesajdan konuşmadaki son mesaja kadar <PERSON>llandırılacaktır.", "com_ui_fork_info_target": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, hede<PERSON> mesaja kadar olan tüm mesajları ve komşularını çatallandırır; di<PERSON><PERSON> bi<PERSON>, tüm mesaj da<PERSON>, g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> olup olmadıklarına veya aynı yoldan olup olmadıklarına bakılmaksızın da<PERSON>dir.", "com_ui_fork_info_visible": "Bu seçenek sadece görünen mesajları çatallandırır; di<PERSON><PERSON> bi<PERSON>, hede<PERSON> mesaja giden do<PERSON> yolu, her<PERSON><PERSON> bir dal o<PERSON>.", "com_ui_fork_processing": "Konuşma çatallanıyor...", "com_ui_fork_remember": "Hat<PERSON>rla", "com_ui_fork_remember_checked": "Seçim<PERSON>z kull<PERSON>ımdan sonra hatırlanacaktır. <PERSON><PERSON><PERSON><PERSON> herhangi bir zamanda bunu değiştirebilirsiniz.", "com_ui_fork_split_target": "Çatallamaya burada ba<PERSON>la", "com_ui_fork_split_target_setting": "Varsayılan olarak hedef me<PERSON> ba<PERSON>la", "com_ui_fork_success": "Başarıyla çatallanmış konuşma", "com_ui_fork_visible": "<PERSON><PERSON><PERSON>", "com_ui_go_back": "<PERSON><PERSON> git", "com_ui_go_to_conversation": "Konuşmaya Git", "com_ui_happy_birthday": "1. <PERSON><PERSON><PERSON> günüm kutlu olsun!", "com_ui_hide_qr": "QR Kodunu Gizle", "com_ui_host": "Host", "com_ui_image_gen": "Görüntü <PERSON>", "com_ui_import_conversation_error": "Konuşmalarınızı içe aktarma sırasında bir hata oluştu", "com_ui_import_conversation_file_type_error": "Desteklenmeyen içe aktarma türü", "com_ui_import_conversation_info": "JSON dosyasından konuşmaları içe aktar", "com_ui_import_conversation_success": "Konuşmalar başarıyla içe aktarıldı", "com_ui_include_shadcnui": "shadcn/ui bileşen talimatlarını dahil et", "com_ui_input": "<PERSON><PERSON><PERSON>", "com_ui_instructions": "Talimatlar", "com_ui_latest_footer": "<PERSON><PERSON> i<PERSON>pa<PERSON>.", "com_ui_latest_production_version": "En son üretim s<PERSON>", "com_ui_latest_version": "En son s<PERSON><PERSON><PERSON><PERSON>", "com_ui_librechat_code_api_key": "LibreChat Kod Yorumlayıcı API anahtarınızı alın", "com_ui_librechat_code_api_subtitle": "Güvenli. Çoklu dil. Giriş/Çıkış Dosyaları.", "com_ui_librechat_code_api_title": "Yapay Zeka Kodu Çalıştır", "com_ui_locked": "<PERSON><PERSON><PERSON>", "com_ui_logo": "{{0}} <PERSON><PERSON><PERSON>", "com_ui_manage": "<PERSON><PERSON><PERSON>", "com_ui_max_tags": "<PERSON>zin verilen maksimum sayı {{0}}, en son de<PERSON><PERSON><PERSON> kull<PERSON>ıyo<PERSON>.", "com_ui_mention": "Bir uç nokta, asistan veya hazır ayar anın, hız<PERSON><PERSON>ca ona geçmek için", "com_ui_min_tags": "<PERSON>ha fazla de<PERSON> kaldı<PERSON>, en az {{0}} gereklidir.", "com_ui_model": "Model", "com_ui_model_parameters": "Model Parametreleri", "com_ui_more_info": "<PERSON><PERSON> fazla bilgi", "com_ui_my_prompts": "İstemlerim", "com_ui_name": "Ad", "com_ui_new_chat": "<PERSON><PERSON>", "com_ui_next": "<PERSON><PERSON><PERSON>", "com_ui_no": "Hay<PERSON><PERSON>", "com_ui_no_bookmarks": "henüz yer iminiz yok gibi görünüyor. <PERSON>ir sohbete tıklayın ve yeni bir tane ekleyin", "com_ui_no_category": "Kategori yok", "com_ui_no_changes": "Güncellenecek değişiklik yok", "com_ui_no_terms_content": "Şartlar ve koşullar için içerik bulunmuyor", "com_ui_nothing_found": "Hiçbir şey bulunamadı", "com_ui_of": "-den", "com_ui_off": "<PERSON><PERSON><PERSON>", "com_ui_on": "Açık", "com_ui_page": "Say<PERSON>", "com_ui_prev": "<PERSON><PERSON><PERSON>", "com_ui_preview": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_privacy_policy": "Gizlilik Politikası", "com_ui_privacy_policy_url": "Gizlilik Politikası URL'si", "com_ui_prompt": "İstem", "com_ui_prompt_already_shared_to_all": "<PERSON>u istem zaten tüm kullanıcılarla paylaşılmış", "com_ui_prompt_name": "İstem Adı", "com_ui_prompt_name_required": "İstem Adı gerekli", "com_ui_prompt_preview_not_shared": "<PERSON><PERSON> bu is<PERSON> için işbirliğine izin vermemiş.", "com_ui_prompt_text": "<PERSON><PERSON>", "com_ui_prompt_text_required": "<PERSON><PERSON>", "com_ui_prompt_update_error": "İstem güncellenirken bir hata oluştu", "com_ui_prompts": "İstemler", "com_ui_prompts_allow_create": "İstem oluşturmaya izin ver", "com_ui_prompts_allow_share_global": "İstemleri tüm kullanıcılarla paylaşmaya izin ver", "com_ui_prompts_allow_use": "İstem kullanımına izin ver", "com_ui_provider": "Sağlayıcı", "com_ui_read_aloud": "<PERSON><PERSON><PERSON> oku", "com_ui_refresh_link": "Bağlantıyı yenile", "com_ui_regenerate": "Yeniden Oluştur", "com_ui_region": "<PERSON><PERSON><PERSON>", "com_ui_rename": "<PERSON><PERSON><PERSON>ı<PERSON>", "com_ui_rename_prompt": "İstemi Yeniden Adlandır", "com_ui_reset_var": "{{0}} sıfırla", "com_ui_result": "<PERSON><PERSON><PERSON>", "com_ui_revoke": "<PERSON><PERSON>", "com_ui_revoke_info": "Kullanıcı tarafından sağlanan tüm kimlik bilgilerini geri al", "com_ui_revoke_key_confirm": "Bu anahtarı iptal etmek istediğinizden emin misiniz?", "com_ui_revoke_key_endpoint": "{{0}} i<PERSON><PERSON>", "com_ui_revoke_keys": "Anahtarları İptal Et", "com_ui_revoke_keys_confirm": "Tüm anahtarları iptal etmek istediğinizden emin misiniz?", "com_ui_role_select": "Rol", "com_ui_run_code": "Kodu Çalıştır", "com_ui_run_code_error": "Kod çalıştırılırken bir hata oluştu", "com_ui_save": "<PERSON><PERSON>", "com_ui_save_submit": "<PERSON><PERSON>", "com_ui_saved": "<PERSON><PERSON><PERSON><PERSON>!", "com_ui_schema": "<PERSON><PERSON>", "com_ui_search": "Ara", "com_ui_select": "Seç", "com_ui_select_file": "<PERSON><PERSON> <PERSON>ya seç", "com_ui_select_model": "Bir model se<PERSON><PERSON>", "com_ui_select_provider": "Bir sağlayıcı seç", "com_ui_select_provider_first": "Önce bir sağlayıcı seçin", "com_ui_select_region": "<PERSON><PERSON> bölge seç", "com_ui_select_search_model": "<PERSON><PERSON><PERSON> model ara", "com_ui_select_search_plugin": "<PERSON><PERSON><PERSON> göre e<PERSON>nti ara", "com_ui_select_search_provider": "Sağlayıcıyı ada göre ara", "com_ui_select_search_region": "<PERSON><PERSON><PERSON><PERSON> ada göre ara", "com_ui_share": "Paylaş", "com_ui_share_create_message": "Adın<PERSON>z ve paylaşım sonrasında eklediğiniz mesajlar gizli kalır.", "com_ui_share_delete_error": "Paylaşılan bağlantı silinirken bir hata oluştu", "com_ui_share_error": "So<PERSON>bet bağlantısını paylaşırken bir hata oluştu", "com_ui_share_form_description": "Form açıklaması paylaş", "com_ui_share_link_to_chat": "Sohbete bağlantı paylaş", "com_ui_share_to_all_users": "<PERSON><PERSON><PERSON> k<PERSON>anıcılarla paylaş", "com_ui_share_update_message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> talim<PERSON>larınız ve paylaşım sonrasında eklediğiniz mesajlar gizli kalır.", "com_ui_share_var": "{{0}} paylaş", "com_ui_shared_link_bulk_delete_success": "Paylaşılan bağlantılar başarı<PERSON> silindi", "com_ui_shared_link_delete_success": "Paylaşılan bağlantı başarıyla silindi", "com_ui_shared_link_not_found": "Paylaşılan bağlantı bulunamadı", "com_ui_shared_prompts": "Paylaşılan İstemler", "com_ui_show_all": "Tümünü <PERSON>ö<PERSON>", "com_ui_show_qr": "QR Kodunu Gö<PERSON>", "com_ui_simple": "<PERSON><PERSON><PERSON>", "com_ui_size": "<PERSON><PERSON>", "com_ui_special_variables": "<PERSON><PERSON>:", "com_ui_speech_while_submitting": "Bir yanıt oluşturulurken konuşma gönderilemez", "com_ui_stop": "<PERSON><PERSON><PERSON>", "com_ui_storage": "<PERSON><PERSON><PERSON>", "com_ui_submit": "<PERSON><PERSON><PERSON>", "com_ui_terms_and_conditions": "Şartlar ve koşullar", "com_ui_terms_of_service": "Hizmet Şartları", "com_ui_thinking": "Düşünüyor...", "com_ui_thoughts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_tools": "Araçlar", "com_ui_unarchive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_unarchive_error": "Konuşmayı arşivden çıkarma başarısız oldu", "com_ui_unknown": "Bilinmeyen", "com_ui_update": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_upload": "<PERSON><PERSON><PERSON>", "com_ui_upload_code_files": "Kod Yorumlayıcı i<PERSON><PERSON>", "com_ui_upload_delay": "\"{{0}}\" yü<PERSON>nmesi beklenenden daha uzun sürüyor. Lütfen dosyanın alma işlemini tamamlamasını bekleyin.", "com_ui_upload_error": "Dosyanızı yüklerken bir hata oluştu", "com_ui_upload_file_search": "<PERSON><PERSON><PERSON>", "com_ui_upload_files": "Dosyaları yükle", "com_ui_upload_image": "<PERSON>ir resim yükle", "com_ui_upload_image_input": "<PERSON><PERSON><PERSON>", "com_ui_upload_invalid": "Geçersiz dosya yükleme. 2 MB'ı geçmeyen bir resim olması gerekir", "com_ui_upload_invalid_var": "Yükleme için geç<PERSON>. {{0}} MB'ı aşmayan bir resim olmalı", "com_ui_upload_success": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_upload_type": "<PERSON><PERSON><PERSON><PERSON> Türü<PERSON>ü <PERSON>", "com_ui_use_micrphone": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>an", "com_ui_use_prompt": "<PERSON><PERSON><PERSON>", "com_ui_variables": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_variables_info": "İstemi kullanırken daha sonra doldurmak üzere metninizde çift süslü parantez kullanın, örn. `{{example variable}}`.", "com_ui_version_var": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{0}}", "com_ui_versions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_view_source": "<PERSON><PERSON>k sohbeti g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_yes": "<PERSON><PERSON>", "com_ui_zoom": "Yakınlaştır", "com_user_message": "<PERSON>"}