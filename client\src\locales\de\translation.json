{"chat_direction_left_to_right": "<PERSON><PERSON> – etwas fehlt noch", "chat_direction_right_to_left": "<PERSON><PERSON> – etwas fehlt noch", "com_a11y_ai_composing": "Die KI erstellt noch ihre Antwort.\n", "com_a11y_end": "Die KI hat die Antwort fertiggestellt.", "com_a11y_start": "Die KI hat mit ihrer Antwort begonnen. ", "com_agents_allow_editing": "Anderen Nutzenden die Bearbeitung deines Agenten erlauben", "com_agents_by_librechat": "<PERSON>", "com_agents_code_interpreter": "<PERSON><PERSON><PERSON>, ermöglicht es deinem Agenten, die LibreChat Code Interpreter API zu nutzen, um generierten Code sicher auszuführen, einschließlich der Verarbeitung von Dateien. Erfordert einen gültigen API-Schlüssel.", "com_agents_code_interpreter_title": "Code-Interpreter-API", "com_agents_create_error": "Bei der Erstellung deines Agenten ist ein Fehler aufgetreten.", "com_agents_description_placeholder": "Optional: <PERSON><PERSON><PERSON><PERSON> hier deinen Agenten", "com_agents_enable_file_search": "Dateisuche aktivieren", "com_agents_file_context": "Datei<PERSON><PERSON><PERSON>x<PERSON> (OCR)", "com_agents_file_context_disabled": "Der Agent muss vor dem Hochladen von Dateien für den Datei-Kontext erstellt werden.", "com_agents_file_context_info": "Als „Kontext“ hochgeladene Dateien werden mit OCR verarbeitet, um Text zu extrahieren, der dann den Anweisungen des Agenten hinzugefügt wird. Ideal für Dokumente, Bilder mit Text oder PDFs, wenn Sie den vollständigen Textinhalt einer Datei benötigen", "com_agents_file_search_disabled": "Der Agent muss erstellt werden, bevor <PERSON> für die Dateisuche hochgeladen werden können.", "com_agents_file_search_info": "<PERSON><PERSON> akt<PERSON><PERSON><PERSON>, wird der Agent über die unten aufgelisteten exakten Dateinamen informiert und kann dadurch relevante Informationen aus diesen Dateien abrufen", "com_agents_instructions_placeholder": "Die Systemanweisungen, die der Agent verwendet", "com_agents_mcp_description_placeholder": "Erkläre in wenigen Worten, was es tut", "com_agents_mcp_icon_size": "Mindestgröße 128 x 128 px", "com_agents_mcp_info": "Füge deinem Agenten MCP-<PERSON> hi<PERSON><PERSON>, damit er Aufgaben ausführen und mit externen Diensten interagieren kann", "com_agents_mcp_name_placeholder": "Eigenes Tool", "com_agents_mcp_trust_subtext": "Benutzerdefinierte Konnektoren werden nicht von LibreChat verifiziert.", "com_agents_mcps_disabled": "Du musst zu<PERSON>t einen Agenten erstellen, bevor du MCPs hinzufügen kannst.", "com_agents_missing_provider_model": "Bitte wählen Si<PERSON> einen Anbieter und ein Modell aus, bevor <PERSON> einen Agenten erstellen.", "com_agents_name_placeholder": "Optional: Der Name des Agenten", "com_agents_no_access": "Du hast keine Berechtigung, diesen Agenten zu bearbeiten.", "com_agents_no_agent_id_error": "<PERSON><PERSON>-ID gefunden. <PERSON><PERSON> stelle sicher, dass der Agent zu<PERSON>t erstellt wurde.", "com_agents_not_available": "Agent nicht verf<PERSON><PERSON>", "com_agents_search_info": "<PERSON><PERSON> diese Funktion aktiviert ist, kann der Agent im Internet nach aktuellen Informationen suchen. Erfordert einen gültigen API-Schlüssel.", "com_agents_search_name": "Agenten nach Namen suchen", "com_agents_update_error": "Beim Aktualisieren deines Agenten ist ein Fehler aufgetreten.", "com_assistants_action_attempt": "Assistent möchte kommunizieren mit {{0}}", "com_assistants_actions": "Aktionen", "com_assistants_actions_disabled": "Du musst einen Assistenten erstellen, bevor du Aktionen hinzufügen kannst.", "com_assistants_actions_info": "Lasse deinen Assistenten Informationen abrufen oder Aktionen über APIs ausführen", "com_assistants_add_actions": "Aktionen hinzufügen", "com_assistants_add_tools": "Werkzeuge hinzufügen", "com_assistants_allow_sites_you_trust": "Erlaube nur Webseiten, denen du vertraust.", "com_assistants_append_date": "Aktuelles Datum & Uhrzeit anhängen", "com_assistants_append_date_tooltip": "<PERSON><PERSON> aktiv<PERSON><PERSON>, werden das aktuelle Client-Datum und die Uhrzeit an die Systemanweisungen des Assistenten angehängt.", "com_assistants_attempt_info": "Assistent möchte Folgendes senden:", "com_assistants_available_actions": "Verfügbare Aktionen", "com_assistants_capabilities": "Fähigkeiten", "com_assistants_code_interpreter": "Code-Interpreter", "com_assistants_code_interpreter_files": "Die folgenden Dateien sind nur für den Code-Interpreter:", "com_assistants_code_interpreter_info": "Der Code-Interpreter ermöglicht es dem Assistenten, Code zu schreiben und auszuführen. Dieses <PERSON> kann Dateien mit verschiedenen Daten und Formatierungen verarbeiten und Dateien wie Grafiken generieren.", "com_assistants_completed_action": "Mit {{0}} gesp<PERSON><PERSON>", "com_assistants_completed_function": "{{0}} ausgeführt", "com_assistants_conversation_starters": "Gesprächseinstiege", "com_assistants_conversation_starters_placeholder": "Gib einen Gesprächseinstieg ein", "com_assistants_create_error": "Bei der Erstellung deines Assistenten ist ein Fehler aufgetreten.", "com_assistants_create_success": "Erfolgreich erstellt", "com_assistants_delete_actions_error": "Beim Löschen der Aktion ist ein Fehler aufgetreten.", "com_assistants_delete_actions_success": "Aktion erfolgreich vom Assistenten gelöscht", "com_assistants_description_placeholder": "Optional: Beschreibe deinen Assistenten hier", "com_assistants_domain_info": "Agent hat diese Information an {{0}} gesendet", "com_assistants_file_search": "Dateisuche", "com_assistants_file_search_info": "Die Dateisuche ermöglicht dem Assistenten, Wissen aus Dateien zu nutzen, die du oder deine Benutzer hochladen. <PERSON><PERSON><PERSON> eine Datei hochgeladen wurde, entscheidet der Assistent automatisch, wann er basierend auf Benutzeranfragen Inhalte abruft. Das Anhängen von Vektor-Speichern für die Dateisuche wird noch nicht unterstützt. Sie können sie vom Provider-Playground aus anhängen oder Dateien für die Dateisuche auf Thread-Basis an Nachrichten anhängen.", "com_assistants_function_use": "Agent hat {{0}} verwendet", "com_assistants_image_vision": "Bildanalyse", "com_assistants_instructions_placeholder": "Die Systemanweisungen, die der Assistent verwendet", "com_assistants_knowledge": "Wissen", "com_assistants_knowledge_disabled": "Der Assistent muss erstellt und Code-Interpreter oder Abruf müssen aktiviert und gespeichert werden, bevor <PERSON> als Wissen hochgeladen werden können.", "com_assistants_knowledge_info": "<PERSON><PERSON> du Dateien unter Wissen hochlädst, kannst du bei Gesprächen mit deinem Assistenten Dateiinhalte einbeziehen.", "com_assistants_max_starters_reached": "Maximale Anzahl an Gesprächseinstiegen erreicht", "com_assistants_name_placeholder": "Optional: Der Name des Assistenten", "com_assistants_non_retrieval_model": "Die Dateisuche ist für dieses KI-Modell nicht aktiviert. Bitte wähle ein anderes Modell aus.", "com_assistants_retrieval": "<PERSON><PERSON><PERSON><PERSON>", "com_assistants_running_action": "Aktion wird ausgeführt", "com_assistants_running_var": "{{0}} aktiv", "com_assistants_search_name": "Assistenten nach Namen suchen", "com_assistants_update_actions_error": "Bei der Erstellung oder Aktualisierung der Aktion ist ein Fehler aufgetreten.", "com_assistants_update_actions_success": "Aktion erfolgreich erstellt oder aktualisiert", "com_assistants_update_error": "Bei der Aktualisierung deines Assistenten ist ein Fehler aufgetreten.", "com_assistants_update_success": "Erfolgreich aktualisiert", "com_auth_already_have_account": "Hast du bereits ein Konto?", "com_auth_apple_login": "Mit Apple anmelden", "com_auth_back_to_login": "Zurück zur Anmeldung", "com_auth_click": "<PERSON><PERSON><PERSON>", "com_auth_click_here": "<PERSON><PERSON><PERSON> hier", "com_auth_continue": "Fortfahren", "com_auth_create_account": "Erstelle dein Konto", "com_auth_discord_login": "<PERSON><PERSON> fort<PERSON>", "com_auth_email": "E-Mail", "com_auth_email_address": "E-Mail-Adresse", "com_auth_email_max_length": "E-Mail sollte nicht länger als 120 Zeichen sein", "com_auth_email_min_length": "E-Mail muss mindestens 6 <PERSON>eichen lang sein", "com_auth_email_pattern": "Du musst eine gültige E-Mail-Adresse eingeben", "com_auth_email_required": "E-Mail ist erforderlich", "com_auth_email_resend_link": "E-Mail erneut senden", "com_auth_email_resent_failed": "Erneutes Senden der Verifizierungs-E-Mail fehlgeschlagen", "com_auth_email_resent_success": "Verifizierungs-E-Mail erfolgreich erneut gesendet", "com_auth_email_verification_failed": "E-Mail-Verifizierung fehlgeschlagen", "com_auth_email_verification_failed_token_missing": "Verifizierung fehlgeschlagen, Token fehlt", "com_auth_email_verification_in_progress": "Ihre E-Mail wird verifiziert, bitte warten", "com_auth_email_verification_invalid": "Ungültige E-Mail-Verifizierung", "com_auth_email_verification_redirecting": "Weiterleitung in {{0}} Sekunden...", "com_auth_email_verification_resend_prompt": "Keine E-Mail erhalten?", "com_auth_email_verification_success": "E-Mail erfolgreich verifiziert", "com_auth_email_verifying_ellipsis": "Überprüfe …", "com_auth_error_create": "Bei der Registrierung deines Kontos ist ein Fehler aufgetreten. Bitte versuche es erneut.", "com_auth_error_invalid_reset_token": "Dieser Passwort-Reset-Token ist nicht mehr gültig.", "com_auth_error_login": "Anmeldung mit den angegebenen Informationen nicht möglich. Bitte überprüfe deine Anmeldedaten und versuche es erneut.", "com_auth_error_login_ban": "Dein <PERSON> wurde aufgrund von V<PERSON>tößen gegen unseren Dienst vorübergehend gesperrt.", "com_auth_error_login_rl": "Zu viele Anmeldeversuche in kurzer Zeit. Bitte versuche es später erneut.", "com_auth_error_login_server": "Es gab einen internen <PERSON>hler. Bitte warte einen Moment und versuche es erneut.", "com_auth_error_login_unverified": "Dein <PERSON> wurde nicht verifiziert. Bitte überprüfe deine E-Mail auf einen Verifizierungslink.", "com_auth_facebook_login": "Mit Facebook fortfahren", "com_auth_full_name": "Vollständiger Name", "com_auth_github_login": "<PERSON><PERSON>", "com_auth_google_login": "Mit Google fortfahren", "com_auth_here": "HIER", "com_auth_login": "Anmelden", "com_auth_login_with_new_password": "Du kannst dich jetzt mit deinem neuen Passwort anmelden.", "com_auth_name_max_length": "Name darf nicht länger als 80 Zeichen sein", "com_auth_name_min_length": "Name muss mindestens 3 <PERSON><PERSON>chen lang sein", "com_auth_name_required": "Name ist erforderlich", "com_auth_no_account": "Du hast noch kein Konto?", "com_auth_password": "Passwort", "com_auth_password_confirm": "Passwort bestätigen", "com_auth_password_forgot": "Passwort vergessen?", "com_auth_password_max_length": "Passwort muss weniger als 128 <PERSON><PERSON><PERSON> lang sein", "com_auth_password_min_length": "Passwort muss mindestens 8 Zeichen lang sein", "com_auth_password_not_match": "Passwörter stimmen nicht überein", "com_auth_password_required": "Passwort ist erforderlich", "com_auth_registration_success_generic": "Bitte überprüfe deine E-Mail, um deine E-Mail-Adresse zu verifizieren.", "com_auth_registration_success_insecure": "Registrierung erfolgreich.", "com_auth_reset_password": "Setze dein Passwort zurück", "com_auth_reset_password_if_email_exists": "Wenn ein Konto mit dieser E-Mail-Adresse existiert, wurde eine E-Mail mit Anweisungen zum Zurücksetzen des Passworts gesendet. Bitte überprüfe auch deinen Spam-Ordner.", "com_auth_reset_password_link_sent": "E-Mail gesendet", "com_auth_reset_password_success": "Passwort erfolgreich zurückgesetzt", "com_auth_saml_login": "Weiter mit SAML", "com_auth_sign_in": "Anmelden", "com_auth_sign_up": "Registrieren", "com_auth_submit_registration": "Registrierung absenden", "com_auth_to_reset_your_password": "um Ihr Passwort zurückzusetzen.", "com_auth_to_try_again": "um es erneut zu versuchen.", "com_auth_two_factor": "Prüfe deine bevorzugte Einmalkennwort-App auf einen Code.", "com_auth_username": "<PERSON><PERSON><PERSON><PERSON> (optional)", "com_auth_username_max_length": "Benutzername darf nicht länger als 20 Zeichen sein", "com_auth_username_min_length": "Benutzername muss mindestens 2 Zeichen lang sein", "com_auth_verify_your_identity": "Bestätige deine Identität", "com_auth_welcome_back": "Willkommen zurück", "com_citation_more_details": "Mehr Details über {{label}}\n", "com_citation_source": "<PERSON><PERSON>\n", "com_click_to_download": "(hier klicken zum Herunterladen)", "com_download_expired": "Download abgelaufen", "com_download_expires": "(hier klicken zum Herunterladen - läuft ab am {{0}})", "com_endpoint": "Endpunkt", "com_endpoint_agent": "Agent", "com_endpoint_agent_model": "Agentenmodell (Empfohlen: GPT-4o-mini)", "com_endpoint_agent_placeholder": "<PERSON>te wähle einen Agenten aus", "com_endpoint_ai": "KI", "com_endpoint_anthropic_maxoutputtokens": "Maximale Anzahl von <PERSON>, die in der Antwort erzeugt werden können. Gib einen niedrigeren Wert für kürzere Antworten und einen höheren Wert für längere Antworten an. Hinweis: Die Modelle können auch vor Erreichen dieses Maximums stoppen.", "com_endpoint_anthropic_prompt_cache": "Prompt-Caching ermöglicht die Wiederverwendung von umfangreichen Kontexten oder Anweisungen über mehrere API-Aufrufe hinweg, wodurch Kosten und Latenzzeiten reduziert werden", "com_endpoint_anthropic_temp": "Reicht von 0 bis 1. Verwende Temperaturen näher an 0 für analytische / Multiple-Choice-Aufgaben und näher an 1 für kreative und generative Aufgaben. Wir empfehlen, entweder dies oder Top P zu ändern, aber nicht beides.", "com_endpoint_anthropic_thinking": "Aktiviert internes logisches Denken für unterstützte Claude-Modelle (3.7 Sonnet). Hinweis: <PERSON><PERSON><PERSON><PERSON>, dass \"Denkbudget\" festgelegt und niedriger als \"Max. Ausgabe-Token\" ist", "com_endpoint_anthropic_thinking_budget": "Bestimmt die maximale An<PERSON>hl an To<PERSON>, die <PERSON> für seinen internen Denkprozess verwenden darf. Ein höheres Budget kann die Antwortqualität verbessern, indem es eine gründlichere Analyse bei komplexen Problemen ermöglicht. <PERSON> nutzt jedoch möglicherweise nicht das gesamte zugewiesene Budget, insbesondere bei Werten über 32.000. Diese Einstellung muss niedriger sein als \"Max. Ausgabe-Token\".", "com_endpoint_anthropic_topk": "Top-k <PERSON><PERSON><PERSON>, wie das Modell Token für die Ausgabe auswählt. Ein Top-k von 1 bedeutet, dass das ausgewählte Token das wahrscheinlichste unter allen Token im Vokabular des Modells ist (auch \"Greedy Decoding\" genannt), während ein Top-k von 3 bedeutet, dass das nächste Token aus den 3 wahrscheinlichsten Token ausgewählt wird (unter Verwendung der Temperatur).", "com_endpoint_anthropic_topp": "Top-p <PERSON><PERSON><PERSON>, wie das Modell Token für die Ausgabe auswählt. Token werden von den wahrscheinlichsten K (siehe topK-Parameter) bis zu den am wenigsten wahrscheinlichen ausgewählt, bis die Summe ihrer Wahrscheinlichkeiten dem Top-p-Wert entspricht.", "com_endpoint_assistant": "Assistent", "com_endpoint_assistant_model": "Assistentenmodell", "com_endpoint_assistant_placeholder": "Bitte wähle einen Assistenten aus dem rechten Seitenpanel aus", "com_endpoint_completion": "Vervollständigung", "com_endpoint_completion_model": "Vervollständigungsmodell (Empfohlen: GPT-4o)", "com_endpoint_config_click_here": "<PERSON><PERSON><PERSON> hier", "com_endpoint_config_google_api_info": "Um deinen Generative Language API-Key (für Gemini) zu erhalten,", "com_endpoint_config_google_api_key": "Google API-Key", "com_endpoint_config_google_cloud_platform": "(von Google Cloud Platform)", "com_endpoint_config_google_gemini_api": "(Gemini API)", "com_endpoint_config_google_service_key": "Google Service Account Key", "com_endpoint_config_key": "API-Key festlegen", "com_endpoint_config_key_encryption": "Dein API-Key wird verschlüsselt und gelöscht um", "com_endpoint_config_key_for": "API-Key festlegen für", "com_endpoint_config_key_google_need_to": "<PERSON> m<PERSON>t", "com_endpoint_config_key_google_service_account": "Ein Service-Konto erstellen", "com_endpoint_config_key_google_vertex_ai": "Vertex AI aktivieren", "com_endpoint_config_key_google_vertex_api": "API auf Google Cloud, dann", "com_endpoint_config_key_google_vertex_api_role": "<PERSON><PERSON> sic<PERSON>, dass du auf \"Erstellen und Fortfahren\" klickst, um mindestens die Rolle \"Vertex AI User\" zu vergeben. E<PERSON><PERSON> zuletzt einen JSON-Key zum Importieren hier.", "com_endpoint_config_key_import_json_key": "Service Account JSON-Key importieren.", "com_endpoint_config_key_import_json_key_invalid": "Ungültiger Service Account JSON-Key. Hast du die richtige Datei importiert?", "com_endpoint_config_key_import_json_key_success": "Service Account JSON-Key erfolgreich importiert", "com_endpoint_config_key_name": "API-Key", "com_endpoint_config_key_never_expires": "Dein API-Key läuft nie ab", "com_endpoint_config_placeholder": "Lege deinen API-Key im Kopfzeilenmenü fest, um zu chatten.", "com_endpoint_config_value": "<PERSON><PERSON> e<PERSON>ben <PERSON>", "com_endpoint_context": "Kontext", "com_endpoint_context_info": "Die maximale Anzahl von <PERSON>, die für den Kontext verwendet werden können. <PERSON>erwen<PERSON> dies zur Kontrolle, wie viele Token pro Anfrage gesendet werden. Wen<PERSON> nicht angegeben, werden Systemstandards basierend auf der bekannten Kontextgröße der Modelle verwendet. Das Setzen höherer Werte kann zu Fehlern und/oder höheren Token-Kosten führen.", "com_endpoint_context_tokens": "Max. <PERSON>-Token", "com_endpoint_custom_name": "Benutzerdefinierter Name", "com_endpoint_default": "Standard", "com_endpoint_default_blank": "Standard: leer", "com_endpoint_default_empty": "Standard: leer", "com_endpoint_default_with_num": "Standard: {{0}}", "com_endpoint_deprecated": "<PERSON><PERSON><PERSON>", "com_endpoint_deprecated_info": "Dieser Endpunkt ist veraltet und wird möglicherweise in zukünftigen Versionen entfernt. Bitte verwende stattdessen den Agent-Endpunkt.", "com_endpoint_deprecated_info_a11y": "Der Plugin-Endpunkt ist veraltet und wird möglicherweise in zukünftigen Versionen entfernt. Bitte verwende stattdessen den Agent-Endpunkt.", "com_endpoint_examples": " Voreinstellungen", "com_endpoint_export": "Exportieren", "com_endpoint_export_share": "Exportieren/Teilen", "com_endpoint_frequency_penalty": "Frequency Penalty", "com_endpoint_func_hover": "<PERSON><PERSON><PERSON><PERSON><PERSON> von Plugins als OpenAI-Funktionen aktivieren", "com_endpoint_google_custom_name_placeholder": "Lege einen benutzerdefinierten Namen für Google fest", "com_endpoint_google_maxoutputtokens": "Maximale Anzahl von <PERSON>, die in der Antwort generiert werden können. Gib einen niedrigeren Wert für kürzere Antworten und einen höheren Wert für längere Antworten an. Hinweis: Modelle können möglicherweise vor Erreichen dieses Maximums stoppen.", "com_endpoint_google_temp": "<PERSON><PERSON><PERSON> Werte = <PERSON><PERSON><PERSON><PERSON><PERSON>, während niedrigere Werte = fokussierter und deterministischer. Wir empfehlen, entweder dies oder Top P zu ändern, aber nicht be<PERSON>.", "com_endpoint_google_thinking": "Aktiviert oder deaktiviert die Argumentation. Diese Einstellung wird nur von bestimmten Modellen (Serie 2.5) unterstützt. Bei älteren Modellen hat diese Einstellung möglicherweise keine Wirkung.", "com_endpoint_google_thinking_budget": "Gibt die Anzahl der Tokens an, die das Modell \"zum Nachdenken\" verwendet. Die tatsächliche Anzahl kann je nach Eingabeaufforderung diesen Wert über- oder unterschreiten.\n\nDiese Einstellung wird nur von bestimmten Modellen (2.5-Serie) unterstützt. Gemini 2.5 Pro unterstützt 128–32.768 Token. Gemini 2.5 Flash unterstützt 0–24.576 Token. Gemini 2.5 Flash Lite unterstützt 512–24.576 Token.\n\n<PERSON><PERSON> lassen oder auf „-1“ setzen, damit das Modell automatisch entscheidet, wann und wie viel nachgedacht werden soll. Standardmäßig denkt Gemini 2.5 Flash Lite nicht.", "com_endpoint_google_topk": "Top-k <PERSON><PERSON><PERSON>, wie das Modell Token für die Antwort auswählt. Ein Top-k von 1 bedeutet, dass das ausgewählte Token das wahrscheinlichste unter allen Token im Vokabular des Modells ist (auch Greedy-Decoding genannt), während ein Top-k von 3 bedeutet, dass das nächste Token aus den 3 wahrscheinlichsten Token ausgewählt wird (unter Verwendung der Temperatur).", "com_endpoint_google_topp": "Top-p <PERSON><PERSON><PERSON>, wie das Modell Token für die Antwort auswählt. Token werden von den wahrscheinlichsten K (siehe topK-Parameter) bis zu den am wenigsten wahrscheinlichen ausgewählt, bis die Summe ihrer Wahrscheinlichkeiten dem Top-p-Wert entspricht.", "com_endpoint_google_use_search_grounding": "<PERSON><PERSON>e den Abgleich mit der Google-Suche, um Antworten auf Basis von Echtzeit-Informationen aus dem Web zu fundieren. Dies ermöglicht den Modellen, auf aktuelle Informationen zuzugreifen und genauere, aktuellere Antworten zu geben.", "com_endpoint_instructions_assistants": "Anweisungen überschreiben", "com_endpoint_instructions_assistants_placeholder": "Überschreibt die Anweisungen des Assistenten. Dies ist nützlich, um das Verhalten auf Basis einzelner Ausführungen zu modifizieren.", "com_endpoint_max_output_tokens": "<PERSON><PERSON>-Token", "com_endpoint_message": "Nachricht an", "com_endpoint_message_new": "<PERSON><PERSON><PERSON><PERSON> an {{0}}", "com_endpoint_message_not_appendable": "Bearbeite deine Nachricht oder generiere neu.", "com_endpoint_my_preset": "<PERSON><PERSON>", "com_endpoint_no_presets": "Noch keine Voreinstellungen, verwende die KI-Einstellungsschaltfläche, um eine Voreinstellung zu erstellen", "com_endpoint_open_menu": "<PERSON><PERSON>", "com_endpoint_openai_custom_name_placeholder": "Lege einen benutzerdefinierten Namen für die KI fest.", "com_endpoint_openai_detail": "Die Auflösung für Bilderkennungs-Anfragen. \"Niedrig\" ist günstiger und schneller, \"Hoch\" ist detaillierter und teurer, und \"Auto\" wählt automatisch zwischen den beiden basierend auf der Bildauflösung.", "com_endpoint_openai_freq": "Zahl zwischen -2,0 und 2,0. Positive Werte bestrafen neue Token basierend auf ihrer bestehenden Häufigkeit im Text, wodurch die Wahrscheinlichkeit des Modells verringert wird, dieselbe Zeile wörtlich zu wiederholen.", "com_endpoint_openai_max": "Die maximale Anzahl zu generierender Token. Die Gesamtlänge der Eingabe-Token und der generierten Token ist durch die Kontextlänge des Modells begrenzt.", "com_endpoint_openai_max_tokens": "Optionales 'max_tokens'-<PERSON><PERSON>, das die maximale An<PERSON>hl von <PERSON> da<PERSON>, die in der Chat-Vervollständigung generiert werden können. Die Gesamtlänge der Eingabe-Token und der generierten Token ist durch die Kontextlänge des Modells begrenzt. Du kannst Fehler erleben, wenn diese Zahl die maximalen Kontext-Token überschreitet.", "com_endpoint_openai_pres": "Zahl zwischen -2,0 und 2,0. Positive Werte bestrafen neue Token basierend darauf, ob sie im bisherigen Text vorkommen, wodurch die Wahrscheinlichkeit des Modells erhöht wird, über neue Themen zu sprechen.", "com_endpoint_openai_prompt_prefix_placeholder": "Lege benutzerdefinierte Anweisungen fest, die in die Systemnachricht an die KI aufgenommen werden sollen. Standard: keine", "com_endpoint_openai_reasoning_effort": "Nur o1- und o3-Modelle: Beschränkt den Schlussfolgerungsaufwand für Schlussfolgerungsmodelle. Ein reduzierter Schlussfolgerungsaufwand kann zu schnelleren Antworten und weniger Tokens führen, die für die Schlussfolgerung in einer Antwort verwendet werden.", "com_endpoint_openai_reasoning_summary": "Nur für Responses-API: Eine Zusammenfassung des Denkprozesses des Modells. Dies kann nützlich sein, um den Denkprozess zu debuggen und zu verstehen. Wähle zwischen \"keine\", \"automatisch\", \"kurz\" oder \"detailliert\".", "com_endpoint_openai_resend": "Alle im Chat zuvor angehängten Bilder mit jeder neuen Nachricht erneut senden. Hinweis: Dies kann die Kosten der Anfrage  aufgrund höherer Token-Anza<PERSON> erheblich erhöhen und du kannst bei vielen Bildanhängen Fehler erleben.", "com_endpoint_openai_resend_files": "Alle im Chat zuvor angehängten Dateien mit jeder neuen Nachricht erneut senden. Hinweis: Dies wird die Kosten der Anfrage aufgrund höherer Token-Anzahl erheblich erhöhen und du kannst bei vielen Anhängen Fehler erleben.", "com_endpoint_openai_stop": "Bis zu 4 Sequenzen, bei denen die API keine weiteren Token generiert.", "com_endpoint_openai_temp": "Entspricht der Kreativität der KI. Höhere Werte = zufälliger und kreativer, während niedrigere Werte = unkreativer und deterministischer. Wir empfehlen, entweder dies oder Top P zu ändern, aber nicht beides. Temperaturen über 1 sind nicht empfehlenswert.", "com_endpoint_openai_topp": "Eine Alternative zum Sampling mit Temperatur, genan<PERSON><PERSON><PERSON>, bei dem das Modell die Ergebnisse der Token mit Top-p-Wahrscheinlichkeitsmasse berücksichtigt. So bedeutet 0,1, dass nur die Token betrachtet werden, die die Top 10% der Wahrscheinlichkeitsmasse ausmachen. Wir empfehlen, entweder dies oder die Temperatur zu ändern, aber nicht beides.", "com_endpoint_openai_use_responses_api": "Nutze die Responses-API anstelle von Chat Completions. Diese API enthält erweiterte Funktionen von OpenAI. Erforderlich für o1-pro, o3-pro und um Zusammenfassungen des Denkprozesses zu aktivieren.", "com_endpoint_openai_use_web_search": "Aktiviere die Websuche über die integrierten Suchfunktionen von OpenAI. Dies ermöglicht es dem Modell, das Web nach aktuellen Informationen zu durchsuchen und präzisere, aktuellere Antworten zu geben.", "com_endpoint_output": "Antwort", "com_endpoint_plug_image_detail": "Bild-Detail", "com_endpoint_plug_resend_files": "Anhänge erneut senden", "com_endpoint_plug_set_custom_instructions_for_gpt_placeholder": "Lege benutzerdefinierte Anweisungen fest, die in die Systemnachricht aufgenommen werden sollen. Standard: keine", "com_endpoint_plug_skip_completion": "Fertigstellung überspringen", "com_endpoint_plug_use_functions": "Funktionen verwenden", "com_endpoint_presence_penalty": "Presence Penalty", "com_endpoint_preset": "Voreinstellung", "com_endpoint_preset_custom_name_placeholder": "<PERSON><PERSON> – etwas fehlt noch", "com_endpoint_preset_default": "ist jetzt die Standardvoreinstellung.", "com_endpoint_preset_default_item": "Standard:", "com_endpoint_preset_default_none": "Keine Standardvoreinstellung aktiv.", "com_endpoint_preset_default_removed": "ist nicht mehr die Standardvoreinstellung.", "com_endpoint_preset_delete_confirm": "B<PERSON> du sicher, dass du diese Voreinstellung löschen möchtest?", "com_endpoint_preset_delete_error": "Beim Löschen deiner Voreinstellung ist ein Fehler aufgetreten. Bitte versuche es erneut.", "com_endpoint_preset_import": "Voreinstellung erfolgreich importiert!", "com_endpoint_preset_import_error": "Beim Importieren deiner Voreinstellung ist ein Fehler aufgetreten. Bitte versuche es erneut.", "com_endpoint_preset_name": "Voreinstellungsname", "com_endpoint_preset_save_error": "<PERSON>im <PERSON>ichern deiner Voreinstellung ist ein Fehler aufgetreten. Bitte versuche es erneut.", "com_endpoint_preset_selected": "Voreinstellung aktiv!", "com_endpoint_preset_selected_title": "Aktiv!", "com_endpoint_preset_title": "Voreinstellung", "com_endpoint_presets": "Voreinstellungen", "com_endpoint_presets_clear_warning": "B<PERSON> du sicher, dass du alle Voreinstellungen löschen möchtest? Dies ist nicht rückgängig zu machen.", "com_endpoint_prompt_cache": "Prompt-Zwischenspeicherung verwenden", "com_endpoint_prompt_prefix": "Benutzerdefinierte Anweisungen", "com_endpoint_prompt_prefix_assistants": "Zusätzliche Anweisungen", "com_endpoint_prompt_prefix_assistants_placeholder": "Lege zusätzliche Anweisungen oder Kontext zusätzlich zu den Hauptanweisungen des Assistenten fest. Wird ignoriert, wenn leer.", "com_endpoint_prompt_prefix_placeholder": "Lege benutzerdefinierte Anweisungen oder Kontext fest. Wird ignoriert, wenn leer.", "com_endpoint_reasoning_effort": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_endpoint_reasoning_summary": "Zusammenfassung des Denkprozesses", "com_endpoint_save_as_preset": "Voreinstellung speichern", "com_endpoint_search": "Endpunkt nach Namen suchen", "com_endpoint_search_endpoint_models": "{{0}} KI-Modelle durchsuchen...", "com_endpoint_search_models": "KI-Modelle durchsuchen...", "com_endpoint_search_var": "{{0}} durchsuchen...", "com_endpoint_set_custom_name": "Lege einen benutzerdefinierten Namen fest, falls du diese Voreinstellung wiederfinden möchtest", "com_endpoint_skip_hover": "Aktiviere das Überspringen des Vervollständigungsschritts, der die endgültige Antwort und die generierten Schritte überprüft", "com_endpoint_stop": "Stop-Sequenzen", "com_endpoint_stop_placeholder": "<PERSON><PERSON><PERSON>wörter durch Drücken der `Enter`-Taste", "com_endpoint_temperature": "Temperatur", "com_endpoint_thinking": "Denken", "com_endpoint_thinking_budget": "Denkbudget", "com_endpoint_top_k": "Top K", "com_endpoint_top_p": "Top P", "com_endpoint_use_active_assistant": "Aktiven Assistenten verwenden", "com_endpoint_use_responses_api": "Responses-API nutzen", "com_endpoint_use_search_grounding": "Fundierung mit Google-Websuche", "com_error_expired_user_key": "Der angegebene API-Key für {{0}} ist am {{1}} abgelaufen. Bitte gebe einen neuen API-Key ein und versuche es erneut.", "com_error_files_dupe": "<PERSON><PERSON><PERSON> er<PERSON>.", "com_error_files_empty": "<PERSON><PERSON> sind nicht zul<PERSON>ssig", "com_error_files_process": "Bei der Verarbeitung der Datei ist ein Fehler aufgetreten.", "com_error_files_unsupported_capability": "<PERSON>ine aktivierten Funktionen unterstützen diesen Dateityp", "com_error_files_upload": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten", "com_error_files_upload_canceled": "Die Datei-Upload-Anfrage wurde abgebrochen. Hinweis: Der Upload-Vorgang könnte noch im Hintergrund laufen und die Datei muss möglicherweise manuell gelöscht werden.", "com_error_files_validation": "Bei der Validierung der Datei ist ein Fehler aufgetreten.", "com_error_google_tool_conflict": "Die integrierten Google-Tools können nicht zusammen mit externen Tools verwendet werden. Bitte deaktiviere entweder die integrierten oder die externen Tools.", "com_error_heic_conversion": "Das HEIC-Bild konnte nicht in JPEG konvertiert werden. Bitte versuchen Sie, das Bild manuell zu konvertieren oder verwenden Sie ein anderes Format.", "com_error_input_length": "Die Anzahl der Tokens der letzten Nachricht ist zu lang und überschreitet das Token-Limit. Oder Ihre Token-Limit-Parameter sind falsch konfiguriert, was sich negativ auf das Kontextfenster auswirkt. Weitere Informationen: {{0}}. Bitte kürzen Sie Ihre Nachricht, passen Sie die maximale Kontextgröße in den Konversationsparametern an oder teilen Sie die Konversation auf, um fortzufahren.", "com_error_invalid_agent_provider": "Der Anbieter \"{{0}}\" steht für die Verwendung mit Agents nicht zur Verfügung. Bitte gehe zu den Einstellungen deines Agents und wähle einen aktuell verfügbaren Anbieter aus.", "com_error_invalid_user_key": "Ungültiger API-Key angegeben. Bitte gebe einen gültigen API-Key ein und versuche es erneut.", "com_error_moderation": "<PERSON><PERSON> sche<PERSON>, dass der eingereichte Inhalt von unserem Moderationssystem als nicht mit unseren Community-Richtlinien vereinbar gekennzeichnet wurde. Wir können mit diesem spezifischen Thema nicht fortfahren. Wenn Sie andere Fragen oder Themen haben, die Si<PERSON> erkunden möchten, bearbeiten Sie bitte Ihre Nachricht oder erstellen Sie eine neue Konversation.", "com_error_no_base_url": "<PERSON><PERSON>-URL gefunden. <PERSON>te gebe eine ein und versuche es erneut.", "com_error_no_user_key": "Kein API-Key gefunden. Bitte gebe einen API-Key ein und versuche es erneut.", "com_files_filter": "<PERSON><PERSON> filtern...", "com_files_no_results": "<PERSON><PERSON>.", "com_files_number_selected": "{{0}} von {{1}} Datei(en) ausgewählt", "com_generated_files": "Generierte Dateien:", "com_hide_examples": "Beispiele ausblenden", "com_info_heic_converting": "HEIC-Bild wird in JPEG konventiert...", "com_nav_2fa": "Zwei-Faktor-Authentifizierung (2FA)", "com_nav_account_settings": "Kontoeinstellungen", "com_nav_always_make_prod": "Neue Versionen direkt produktiv nehmen", "com_nav_archive_created_at": "Archivierungsdatum", "com_nav_archive_name": "Name", "com_nav_archived_chats": "Archivierte Cha<PERSON>", "com_nav_at_command": "@-<PERSON><PERSON><PERSON>", "com_nav_at_command_description": "Schaltet den Befehl \"@\" zum Wechsel<PERSON> von <PERSON>, <PERSON><PERSON>, Voreinstellungen usw. um.", "com_nav_audio_play_error": "<PERSON><PERSON> beim Abspielen des Audios: {{0}}", "com_nav_audio_process_error": "Fehler bei der Verarbeitung des Audios: {{0}}", "com_nav_auto_scroll": "Automatisch zur neuesten Nachricht scrollen, wenn der Chat geöffnet wird", "com_nav_auto_send_prompts": "Prompts automatisch senden", "com_nav_auto_send_text": "Text automatisch senden", "com_nav_auto_send_text_disabled": "-1 setzen zum Deaktivieren", "com_nav_auto_transcribe_audio": "Audio automatisch transkribieren", "com_nav_automatic_playback": "Automatische Wiedergabe der neuesten Nachricht", "com_nav_balance": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_balance_auto_refill_disabled": "Automatisches Auffüllen ist deaktiviert", "com_nav_balance_auto_refill_error": "Die Einstellungen ", "com_nav_balance_auto_refill_settings": "Einstellungen zum automatischen Auffüllen", "com_nav_balance_day": "Tag", "com_nav_balance_days": "Tage", "com_nav_balance_every": "<PERSON><PERSON>", "com_nav_balance_hour": "Stunde", "com_nav_balance_hours": "Stunden", "com_nav_balance_interval": "Intervall", "com_nav_balance_last_refill": "Letztes Auffüllen:", "com_nav_balance_minute": "Minute", "com_nav_balance_minutes": "Minuten", "com_nav_balance_month": "<PERSON><PERSON>", "com_nav_balance_months": "Monate", "com_nav_balance_next_refill": "Nächstes Auffüllen:", "com_nav_balance_next_refill_info": "Die nächste Aufladung erfolgt nur dann automatisch, wenn beide Bedingungen erfüllt sind: Das angegebene Zeitintervall ist seit der letzten Aufladung verstrichen, und das Senden einer Aufforderung würde dazu führen, dass das Guthaben unter Null sinkt.", "com_nav_balance_refill_amount": "Nachfüllmenge:", "com_nav_balance_second": "Sekunde", "com_nav_balance_seconds": "Sekunden", "com_nav_balance_week": "<PERSON><PERSON><PERSON>", "com_nav_balance_weeks": "<PERSON><PERSON><PERSON>", "com_nav_browser": "Browser", "com_nav_center_chat_input": "Chat-Eingabe im Willkommensbildschirm zentrieren", "com_nav_change_picture": "Bild ändern", "com_nav_chat_commands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_chat_commands_info": "<PERSON><PERSON> Be<PERSON>hle werden aktiviert, indem du bestimmte Zeichen am Anfang deiner Nachricht eingibst. <PERSON>er Befehl wird durch sein festgelegtes Präfix ausgelöst. Du kannst sie deaktivieren, wenn du diese Zeichen häufig zum Beginn deiner Nachrichten verwendest.", "com_nav_chat_direction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_clear_all_chats": "Alle Chats löschen", "com_nav_clear_cache_confirm_message": "B<PERSON> du sicher, dass du den Cache löschen möchtest?", "com_nav_clear_conversation": "Konversationen löschen", "com_nav_clear_conversation_confirm_message": "B<PERSON> du sicher, dass du alle Konversationen löschen möchtest? Dies ist nicht rückgängig zu machen.", "com_nav_close_sidebar": "Seitenleiste schließen", "com_nav_commands": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_confirm_clear": "Löschen bestätigen", "com_nav_conversation_mode": "Konversationsmodus", "com_nav_convo_menu_options": "Optionen des Gesprächsmenüs", "com_nav_db_sensitivity": "Dezibel-Empfindlichkeit", "com_nav_delete_account": "Konto löschen", "com_nav_delete_account_button": "Mein Ko<PERSON> dauerhaft löschen", "com_nav_delete_account_confirm": "Konto löschen - bist du sicher?", "com_nav_delete_account_email_placeholder": "Bitte gebe deine Konto-E-Mail ein", "com_nav_delete_cache_storage": "TTS-Cache-Speicher löschen", "com_nav_delete_data_info": "Alle deine Daten werden gelöscht.", "com_nav_delete_warning": "WARNUNG: Die<PERSON> wird dein Konto dauerhaft löschen.", "com_nav_enable_cache_tts": "TTS-Caching aktivieren", "com_nav_enable_cloud_browser_voice": "Cloud-b<PERSON><PERSON><PERSON> verwenden", "com_nav_enabled": "Aktiviert", "com_nav_engine": "Engine", "com_nav_enter_to_send": "<PERSON><PERSON> dr<PERSON><PERSON>, um Nachrichten zu senden", "com_nav_export": "Exportieren", "com_nav_export_all_message_branches": "Alle Nachrichtenzweige exportieren", "com_nav_export_conversation": "Konversation exportieren", "com_nav_export_filename": "Dateiname", "com_nav_export_filename_placeholder": "Lege den Dateinamen fest", "com_nav_export_include_endpoint_options": "Endpunktoptionen einbeziehen", "com_nav_export_recursive": "Rekursiv", "com_nav_export_recursive_or_sequential": "Rekursiv oder sequentiell?", "com_nav_export_type": "<PERSON><PERSON>", "com_nav_external": "Extern", "com_nav_font_size": "Schriftgröße", "com_nav_font_size_base": "<PERSON><PERSON><PERSON>", "com_nav_font_size_lg": "<PERSON><PERSON><PERSON>", "com_nav_font_size_sm": "<PERSON>", "com_nav_font_size_xl": "<PERSON><PERSON> groß", "com_nav_font_size_xs": "<PERSON><PERSON> klein", "com_nav_help_faq": "Hilfe & FAQ", "com_nav_hide_panel": "Rechte Seitenleiste verstecken", "com_nav_info_balance": "Der Saldo zeigt an, wie viele Token-Credits Sie noch verwenden können. Token-Credits werden in Geldwert umgerechnet (z. B. 1000 Credits = 0,001 USD).", "com_nav_info_code_artifacts": "Aktiviert die Anzeige experimenteller Code-Artefakte neben dem Chat", "com_nav_info_code_artifacts_agent": "Aktiviert die Verwendung von Code-Artefakten für diesen Agenten. Standardmäßig werden zusätzliche, spezielle Anweisungen für die Nutzung von Artefakten hinzugefügt, es sei denn, der \"Benutzerdefinierte Prompt-Modus\" ist aktiviert.", "com_nav_info_custom_prompt_mode": "<PERSON><PERSON> akt<PERSON>, wird die Standard-Systemaufforderung für Artefakte nicht eingeschlossen. Alle Anweisungen zur Erzeugung von Artefakten müssen in diesem Modus manuell bereitgestellt werden.", "com_nav_info_enter_to_send": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, sendet das Drücken von `ENTER` <PERSON><PERSON><PERSON>. <PERSON><PERSON>, fügt das <PERSON> von Enter eine neue Zeile hinzu, und du musst `STRG + ENTER` dr<PERSON><PERSON>, um deine Nachricht zu senden.", "com_nav_info_fork_change_default": "`Nur sichtbare Nachrichten` umfasst nur den direkten Pfad zur ausgewählten Nachricht. `Zugehörige Verzweigungen einbeziehen` fügt Verzweigungen entlang des Pfades hinzu. `Alle bis/von hier einbeziehen` umfasst alle verbundenen Nachrichten und Verzweigungen.", "com_nav_info_fork_split_target_setting": "<PERSON><PERSON> a<PERSON><PERSON>, beginnt das Abzweigen von der Zielnachricht bis zur letzten Nachricht in der Konversation, gemäß dem ausgewählten Verhalten.", "com_nav_info_include_shadcnui": "<PERSON><PERSON> akt<PERSON><PERSON><PERSON>, werden Anweisungen zur Verwendung von shadcn/ui-Komponenten eingeschlossen. shadcn/ui ist eine Sammlung wiederverwendbarer Komponenten, die mit Radix UI und Tailwind CSS erstellt wurden. Hinweis: Dies sind umfangreiche Anweisungen, die Sie nur aktivieren sollten, wenn es Ihnen wichtig ist, das KI-Modell über die korrekten Importe und Komponenten zu informieren. Weitere Informationen zu diesen Komponenten finden Sie unter: https://ui.shadcn.com/", "com_nav_info_latex_parsing": "<PERSON><PERSON><PERSON>, wird LaTeX-Code in Nachrichten als mathematische Gleichungen gerendert. Das Deaktivieren kann die Leistung verbessern, wenn du keine LaTeX-Darstellung benötigst.", "com_nav_info_save_badges_state": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, wird der Status der Chat-Badges gespeichert. <PERSON> bedeutet, dass die Badges im gleichen Status wie im vorherigen Chat bleiben, wenn du einen neuen Chat erstellst. Wenn du diese Option deaktivierst, werden die Badges jedes Mal, wenn du einen neuen Chat erstellst, auf ihren Standardzustand zurückgesetzt.", "com_nav_info_save_draft": "<PERSON><PERSON> akt<PERSON><PERSON>, werden der Text und die Anhänge, die du in das Chat-Formular eingibst, automatisch lokal als Entwürfe gespeichert. Diese Entwürfe sind auch verfügbar, wenn du die Seite neu lädst oder zu einer anderen Konversation wechseln. Entwürfe werden lokal auf deinem Gerät gespeichert und werden gelöscht, sobald die Nachricht gesendet wird.", "com_nav_info_show_thinking": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, sind die Denkprozess-Dropdowns standardmäß<PERSON> geöffnet, sodass du die Gedankengänge der KI in Echtzeit sehen kannst. <PERSON><PERSON> de<PERSON>, bleiben sie standardmäßig geschlossen, für eine übersichtlichere Oberfläche.", "com_nav_info_user_name_display": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, wird der Benutzername des Absenders über jeder Nachricht angezeigt, die du sendest. <PERSON><PERSON>, siehst du nur \"<PERSON>\" über deinen Nachrichten.", "com_nav_lang_arabic": "العربية", "com_nav_lang_auto": "Automatisch erkennen", "com_nav_lang_brazilian_portuguese": "Portuguê<PERSON>", "com_nav_lang_catalan": "Català", "com_nav_lang_chinese": "中文", "com_nav_lang_czech": "Čeština", "com_nav_lang_danish": "Dansk", "com_nav_lang_dutch": "Nederlands", "com_nav_lang_english": "English", "com_nav_lang_estonian": "<PERSON><PERSON><PERSON> keel", "com_nav_lang_finnish": "<PERSON><PERSON>", "com_nav_lang_french": "Français ", "com_nav_lang_georgian": "ქართული", "com_nav_lang_german": "De<PERSON>ch", "com_nav_lang_hebrew": "עברית", "com_nav_lang_hungarian": "Ungarisch", "com_nav_lang_indonesia": "Indonesia", "com_nav_lang_italian": "Italiano", "com_nav_lang_japanese": "日本語", "com_nav_lang_korean": "한국어", "com_nav_lang_persian": "<PERSON><PERSON><PERSON>", "com_nav_lang_polish": "<PERSON><PERSON>", "com_nav_lang_portuguese": "Português", "com_nav_lang_russian": "Русский", "com_nav_lang_spanish": "Español", "com_nav_lang_swedish": "Svenska", "com_nav_lang_thai": "ไทย", "com_nav_lang_traditional_chinese": "繁體中文", "com_nav_lang_turkish": "Türkçe", "com_nav_lang_vietnamese": "Tiếng <PERSON>", "com_nav_language": "<PERSON><PERSON><PERSON>", "com_nav_latex_parsing": "LaTeX in Nachrichten parsen (kann die Leistung beeinflussen)", "com_nav_log_out": "Abmelden", "com_nav_long_audio_warning": "Längere Texte benötigen mehr Zeit zur Verarbeitung.", "com_nav_maximize_chat_space": "Chat-<PERSON><PERSON><PERSON> maximieren", "com_nav_mcp_vars_update_error": "Fehler beim Aktualisieren der benutzerdefinierten MCP-Benutzervariablen: {{0}}", "com_nav_mcp_vars_updated": "Die MCP-Benutzervariablen wurden erfolgreich aktualisiert.", "com_nav_modular_chat": "Ermöglicht das Wechseln der Endpunkte mitten im Gespräch", "com_nav_my_files": "<PERSON><PERSON>", "com_nav_not_supported": "<PERSON>cht unterstützt", "com_nav_open_sidebar": "Seitenleiste öffnen", "com_nav_playback_rate": "Audio-Wiedergabegeschwindigkeit", "com_nav_plugin_auth_error": "<PERSON><PERSON> <PERSON> V<PERSON>uch, dies<PERSON> zu authentifizieren, ist ein <PERSON>hler aufgetreten. Bitte versuche es erneut.", "com_nav_plugin_install": "Installieren", "com_nav_plugin_search": "Plugins suchen", "com_nav_plugin_store": "Plugin-Store", "com_nav_plugin_uninstall": "Deinstallieren", "com_nav_plus_command": "+-<PERSON><PERSON><PERSON>", "com_nav_plus_command_description": "Schaltet den Befehl \"+\" zum Hinzufügen einer Mehrfachantwort-Einstellung um", "com_nav_profile_picture": "Profilbild", "com_nav_save_badges_state": "Badgestatus speichern", "com_nav_save_drafts": "Entwürfe lokal speichern", "com_nav_scroll_button": "Zum Ende scrollen <PERSON>ton", "com_nav_search_placeholder": "Nachrichten durchsuchen", "com_nav_send_message": "Nachricht senden", "com_nav_setting_account": "Ko<PERSON>", "com_nav_setting_balance": "<PERSON><PERSON>", "com_nav_setting_beta": "Beta-Funktionen", "com_nav_setting_chat": "Cha<PERSON>", "com_nav_setting_data": "Datensteuerung", "com_nav_setting_general": "Allgemein", "com_nav_setting_mcp": "MCP Einstellungen", "com_nav_setting_personalization": "Personalisierung", "com_nav_setting_speech": "<PERSON><PERSON><PERSON>", "com_nav_settings": "Einstellungen", "com_nav_shared_links": "Geteilte Links", "com_nav_show_code": "Code immer anzeigen, wenn der Code-Interpreter verwendet wird", "com_nav_show_thinking": "Denkprozess-Dropdowns standardmäßig öffnen", "com_nav_slash_command": "/-<PERSON><PERSON><PERSON>", "com_nav_slash_command_description": "Schaltet den Befehl \"/\" zur Auswahl einer Eingabeaufforderung über die Tastatur um", "com_nav_speech_to_text": "<PERSON><PERSON><PERSON> zu Text", "com_nav_stop_generating": "Generierung stoppen", "com_nav_text_to_speech": "Text zu Sprache", "com_nav_theme": "Design", "com_nav_theme_dark": "<PERSON><PERSON><PERSON>", "com_nav_theme_light": "Hell", "com_nav_theme_system": "System", "com_nav_tool_dialog": "Assistenten-Werkzeuge", "com_nav_tool_dialog_agents": "Agent-<PERSON><PERSON>", "com_nav_tool_dialog_description": "Assistent muss gespeichert werden, um Werkzeugauswahlen zu speichern.", "com_nav_tool_remove": "Entfernen", "com_nav_tool_search": "Werkzeuge suchen", "com_nav_user": "BENUTZER", "com_nav_user_msg_markdown": "Benutzernachrichten als Markdown darstellen", "com_nav_user_name_display": "Benutzernamen in Nachrichten anzeigen", "com_nav_voice_select": "Stimme", "com_show_agent_settings": "Agenteneinstellungen anzeigen", "com_show_completion_settings": "Vervollständigungseinstellungen anzeigen", "com_show_examples": "Beispiele anzeigen", "com_sidepanel_agent_builder": "<PERSON><PERSON>", "com_sidepanel_assistant_builder": "Assistenten-Ersteller", "com_sidepanel_attach_files": "<PERSON><PERSON>", "com_sidepanel_conversation_tags": "Lesezeichen", "com_sidepanel_hide_panel": "Seitenleiste ausblenden", "com_sidepanel_manage_files": "<PERSON><PERSON> ver<PERSON>", "com_sidepanel_mcp_enter_value": "<PERSON><PERSON> den Wert für {{0}} ein", "com_sidepanel_mcp_no_servers_with_vars": "<PERSON><PERSON>P-Server mit konfigurierbaren Variablen.", "com_sidepanel_mcp_variables_for": "MCP Variablen für {{0}}", "com_sidepanel_parameters": "KI-Einstellungen", "com_sources_image_alt": "Suchergebnis Bild\n", "com_sources_more_sources": "+{{count}} <PERSON><PERSON>\n", "com_sources_tab_all": "Alles", "com_sources_tab_images": "Bilder", "com_sources_tab_news": "Nachrichten", "com_sources_title": "<PERSON><PERSON>\n", "com_ui_2fa_account_security": "Die Zwei-Faktor-Authentifizierung bietet Ihrem Konto eine zusätzliche Sicherheitsebene.", "com_ui_2fa_disable": "2FA deaktivieren", "com_ui_2fa_disable_error": "Beim Deaktivieren der Zwei-Faktor-Authentifizierung ist ein Fehler aufgetreten.", "com_ui_2fa_disabled": "2FA wurde deaktiviert.", "com_ui_2fa_enable": "2FA aktivieren", "com_ui_2fa_enabled": "2FA wurde aktiviert.", "com_ui_2fa_generate_error": "<PERSON>im Erstellen der Einstellungen für die Zwei-Faktor-Authentifizierung ist ein Fehler aufgetreten.", "com_ui_2fa_invalid": "Ungültiger Zwei-Faktor-Authentifizierungscode.", "com_ui_2fa_setup": "2FA einrichten", "com_ui_2fa_verified": "Die Zwei-Faktor-Authentifizierung wurde erfolgreich verifiziert.", "com_ui_accept": "<PERSON>ch akzeptiere", "com_ui_action_button": "Aktionstaste", "com_ui_add": "Hinzufügen", "com_ui_add_mcp": "MCP hinzufügen", "com_ui_add_mcp_server": "MCP Server hinzufügen", "com_ui_add_model_preset": "Ein KI-Modell oder eine Voreinstellung für eine zusätzliche Antwort hinzufügen", "com_ui_add_multi_conversation": "Mehrere Chats hinzufügen", "com_ui_adding_details": "<PERSON><PERSON><PERSON><PERSON><PERSON>\n", "com_ui_admin": "Admin", "com_ui_admin_access_warning": "Das Deaktivieren des Admin-Zugriffs auf diese Funktion kann zu unerwarteten Problemen in der Benutzeroberfläche führen, die ein Neuladen erfordern. Nach dem Speichern kann dies nur über die Schnittstelleneinstellung in der librechat.yaml-Konfiguration rückgängig gemacht werden, was sich auf alle Rollen auswirkt.", "com_ui_admin_settings": "Admin-Einstellungen", "com_ui_advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_advanced_settings": "Erweiterte Einstellungen", "com_ui_agent": "Agent", "com_ui_agent_chain": "Agent<PERSON><PERSON><PERSON>", "com_ui_agent_chain_info": "Ermöglicht das Erstellen von Agenten-Sequenzen. Jeder Agent kann auf die Ausgaben vorheriger Agenten in der Kette zugreifen. Basiert auf der \"Mixture-of-Agents\"-Architektur, bei der Agenten vorherige Ausgaben als zusätzliche Informationen verwenden.", "com_ui_agent_chain_max": "Du hast die maximale <PERSON><PERSON><PERSON> von {{0}} <PERSON><PERSON> erreicht.", "com_ui_agent_delete_error": "Beim Löschen des Assistenten ist ein Fehler aufgetreten", "com_ui_agent_deleted": "Agent er<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_agent_duplicate_error": "<PERSON>im <PERSON>plizieren des Assistenten ist ein Fehler aufgetreten", "com_ui_agent_duplicated": "Agent wurde er<PERSON><PERSON><PERSON><PERSON><PERSON> du<PERSON>", "com_ui_agent_editing_allowed": "Andere Nutzende können diesen Agenten bereits bearbeiten", "com_ui_agent_recursion_limit": "Maximale Agenten-<PERSON>hritte", "com_ui_agent_recursion_limit_info": "<PERSON><PERSON><PERSON><PERSON>, wie viele Schritte der Agent in einem Durchlauf ausführen kann, bevor er eine endgültige Antwort gibt. Der Standardwert ist 25 Schritte. Ein Schritt ist entweder eine KI-API-Anfrage oder eine Werkzeugnutzungsrunde. Eine einfache Werkzeuginteraktion umfasst beispielsweise 3 Schritte: die ursprüngliche Anfrage, die Werkzeugnutzung und die Folgeanfrage.", "com_ui_agent_shared_to_all": "Hier muss etwas eingegeben werden. War leer.", "com_ui_agent_var": "{{0}} Agent", "com_ui_agent_version": "Version", "com_ui_agent_version_active": "Aktive Version\n", "com_ui_agent_version_duplicate": "Doppelte Version entdeckt. Dies würde eine Version erzeugen, die identisch mit der Version {{versionIndex}} ist.", "com_ui_agent_version_empty": "<PERSON><PERSON>en verfügbar\n", "com_ui_agent_version_error": "Fehler beim Abrufen der Versionen", "com_ui_agent_version_history": "Versionsgeschichte\n", "com_ui_agent_version_no_agent": "Kein Agent ausgewählt. Bitte wähle einen Agenten aus, um den Versionsverlauf anzuzeigen.", "com_ui_agent_version_no_date": "Datum nicht verfügbar\n", "com_ui_agent_version_restore": "Wiederherstellen\n", "com_ui_agent_version_restore_confirm": "Bist du sicher, dass du diese Version wiederherstellen willst?", "com_ui_agent_version_restore_error": "Die Version konnte nicht wiederhergestellt werden", "com_ui_agent_version_restore_success": "Die Version wurde erfolgreich hergestellt", "com_ui_agent_version_title": "Version {{versionNumber}}", "com_ui_agent_version_unknown_date": "Unbekanntes Datum\n", "com_ui_agents": "<PERSON><PERSON>", "com_ui_agents_allow_create": "Erlaube Agenten zu erstellen", "com_ui_agents_allow_share_global": "Erlaube das Teilen von Agenten mit allen Nutzern", "com_ui_agents_allow_use": "<PERSON><PERSON><PERSON><PERSON><PERSON> von <PERSON>", "com_ui_all": "alle", "com_ui_all_proper": "Alle", "com_ui_analyzing": "<PERSON><PERSON><PERSON>", "com_ui_analyzing_finished": "Analy<PERSON> ab<PERSON><PERSON>", "com_ui_api_key": "API-Schlüssel", "com_ui_archive": "Archivieren", "com_ui_archive_delete_error": "Archivierter Chat konnte nicht gelöscht werden.", "com_ui_archive_error": "Konversation konnte nicht archiviert werden", "com_ui_artifact_click": "Zum Öffnen klicken", "com_ui_artifacts": "Artefakte", "com_ui_artifacts_toggle": "Artefakte-Funktion einschalten", "com_ui_artifacts_toggle_agent": "Artefakte aktivieren", "com_ui_ascending": "Aufsteigend", "com_ui_assistant": "Assistent", "com_ui_assistant_delete_error": "Beim Löschen des Assistenten ist ein Fehler aufgetreten", "com_ui_assistant_deleted": "Assistent erfo<PERSON><PERSON><PERSON><PERSON>", "com_ui_assistants": "Assistenten", "com_ui_assistants_output": "Assistenten-Ausgabe", "com_ui_attach_error": "Datei kann nicht angehängt werden. <PERSON><PERSON><PERSON> oder wähle einen Chat oder versuche, die Seite zu aktualisieren.", "com_ui_attach_error_openai": "Assistentendateien können nicht an andere Endpunkte angehängt werden", "com_ui_attach_error_size": "Dateigrößenlimit für Endpunkt überschritten:", "com_ui_attach_error_type": "<PERSON>cht unterstützter Dateityp für Endpunkt:", "com_ui_attach_remove": "<PERSON><PERSON> ent<PERSON>nen", "com_ui_attach_warn_endpoint": "Nicht-Assistentendateien werden möglicherweise ohne kompatibles Werkzeug ignoriert", "com_ui_attachment": "<PERSON><PERSON>", "com_ui_auth_type": "Authentifizierungstyp", "com_ui_auth_url": "Autorisierungs-URL", "com_ui_authentication": "Authentifizierung", "com_ui_authentication_type": "Authentifizierungstyp", "com_ui_auto": "Auto", "com_ui_available_tools": "Verfügbar<PERSON>", "com_ui_avatar": "Avatar", "com_ui_azure": "Azure", "com_ui_back": "Zurück", "com_ui_back_to_chat": "<PERSON><PERSON><PERSON> zum Chat", "com_ui_back_to_prompts": "<PERSON><PERSON><PERSON> zu den Prompts", "com_ui_backup_codes": "Backup-Codes", "com_ui_backup_codes_regenerate_error": "<PERSON>im <PERSON>eu<PERSON>tellen der Backup-Codes ist ein Fehler aufgetreten.", "com_ui_backup_codes_regenerated": "Backup-Codes wurden erfolgreich neu erstellt.", "com_ui_basic": "Basic", "com_ui_basic_auth_header": "Basic-Authentifizierungsheader", "com_ui_bearer": "Bearer", "com_ui_bookmark_delete_confirm": "B<PERSON> du sicher, dass du dieses Lesezeichen löschen möchtest?", "com_ui_bookmarks": "Lesezeichen", "com_ui_bookmarks_add": "Lesezeichen hinzufügen", "com_ui_bookmarks_add_to_conversation": "Zur aktuellen Konversation hinzufügen", "com_ui_bookmarks_count": "<PERSON><PERSON><PERSON>", "com_ui_bookmarks_create_error": "<PERSON><PERSON> des Lesezeichens ist ein Fehler aufgetreten", "com_ui_bookmarks_create_exists": "Dieses Lesezeichen existiert bereits", "com_ui_bookmarks_create_success": "Lesezeichen erfolgreich erstellt", "com_ui_bookmarks_delete": "Lesezeichen löschen", "com_ui_bookmarks_delete_error": "Beim Löschen des Lesezeichens ist ein Fehler aufgetreten", "com_ui_bookmarks_delete_success": "Lesezeichen erfolgreich <PERSON>", "com_ui_bookmarks_description": "Beschreibung", "com_ui_bookmarks_edit": "Lesezeichen bearbeiten", "com_ui_bookmarks_filter": "Lesezeichen filtern...", "com_ui_bookmarks_new": "<PERSON>eues Lesezei<PERSON>", "com_ui_bookmarks_title": "Titel", "com_ui_bookmarks_update_error": "Beim Aktualisieren des Lesezeichens ist ein Fehler aufgetreten", "com_ui_bookmarks_update_success": "Lesezeichen erfolgreich aktualisiert", "com_ui_bulk_delete_error": "Geteilte Links konnten nicht gelöscht werden.", "com_ui_callback_url": "Callback-URL", "com_ui_cancel": "Abbrechen", "com_ui_cancelled": "Abgebrochen", "com_ui_category": "<PERSON><PERSON><PERSON>\n", "com_ui_chat": "Cha<PERSON>", "com_ui_chat_history": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_clear": "Löschen", "com_ui_clear_all": "Auswahl löschen", "com_ui_client_id": "Client-ID", "com_ui_client_secret": "Client Secret", "com_ui_close": "Schließen", "com_ui_close_menu": "<PERSON><PERSON> sch<PERSON>ßen", "com_ui_close_window": "<PERSON><PERSON> s<PERSON>", "com_ui_code": "Code", "com_ui_collapse_chat": "<PERSON><PERSON>", "com_ui_command_placeholder": "Optional: <PERSON><PERSON> einen <PERSON>hl ein oder den Namen.", "com_ui_command_usage_placeholder": "<PERSON><PERSON><PERSON><PERSON> einen Prompt nach Befehl oder Name aus", "com_ui_complete_setup": "Einrichtung abschließen", "com_ui_configure_mcp_variables_for": "Konfiguriere Variablen für {{0}}", "com_ui_confirm_action": "Aktion bestätigen", "com_ui_confirm_admin_use_change": "Wenn du diese Einstellung änderst, wird der Zugriff für Administratoren, e<PERSON><PERSON><PERSON><PERSON><PERSON> dir sel<PERSON>t, g<PERSON><PERSON><PERSON>. <PERSON><PERSON> du sicher, dass du fortfahren möchtest?", "com_ui_confirm_change": "Änderung bestätigen", "com_ui_context": "Kontext", "com_ui_continue": "Fortfahren", "com_ui_controls": "Steuerung", "com_ui_convo_delete_error": "Unterhaltung konnte nicht gelöscht werden.", "com_ui_copied": "Kopiert!", "com_ui_copied_to_clipboard": "In die Zwischenablage kopiert", "com_ui_copy_code": "Code kopieren", "com_ui_copy_link": "<PERSON>", "com_ui_copy_to_clipboard": "In die Zwischenablage kopieren", "com_ui_create": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_create_link": "<PERSON>", "com_ui_create_memory": "Gedächtnis erstellen", "com_ui_create_prompt": "Prompt erstellen", "com_ui_creating_image": "Bild wird erstellt. Kann einen Moment dauern", "com_ui_current": "Aktuell", "com_ui_currently_production": "Aktuell im Produktivbetrieb", "com_ui_custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_custom_header_name": "Benutzerdefinierter Headername", "com_ui_custom_prompt_mode": "Benutzerdefinierter Promptmodus für Artefakte", "com_ui_dashboard": "Dashboard", "com_ui_date": "Datum", "com_ui_date_april": "April", "com_ui_date_august": "August", "com_ui_date_december": "Dezember", "com_ui_date_february": "<PERSON><PERSON><PERSON>", "com_ui_date_january": "<PERSON><PERSON><PERSON>", "com_ui_date_july": "<PERSON><PERSON>", "com_ui_date_june": "<PERSON><PERSON>", "com_ui_date_march": "<PERSON><PERSON><PERSON>", "com_ui_date_may": "<PERSON>", "com_ui_date_november": "November", "com_ui_date_october": "Oktober", "com_ui_date_previous_30_days": "Letzte 30 Tage", "com_ui_date_previous_7_days": "Letzte 7 Tage", "com_ui_date_september": "September", "com_ui_date_today": "<PERSON><PERSON>", "com_ui_date_yesterday": "Gestern", "com_ui_decline": "Ich akzeptiere nicht", "com_ui_default_post_request": "Standard (POST-Anfrage)", "com_ui_delete": "Löschen", "com_ui_delete_action": "Aktion löschen", "com_ui_delete_action_confirm": "B<PERSON> du sicher, dass du diese Aktion löschen möchtest?", "com_ui_delete_agent_confirm": "B<PERSON> du sicher, dass du diesen Agenten löschen möchtest?", "com_ui_delete_assistant_confirm": "B<PERSON> du sicher, dass du diesen Assistenten löschen möchtest? Dies kann nicht rückgängig gemacht werden.", "com_ui_delete_confirm": "Dies wird löschen:", "com_ui_delete_confirm_prompt_version_var": "Dies wird die ausgewählte Version für \"{{0}}\" löschen. Wenn keine anderen Versionen existieren, wird der Prompt gelöscht.", "com_ui_delete_conversation": "Chat l<PERSON>?", "com_ui_delete_mcp": "MCP löschen", "com_ui_delete_mcp_confirm": "<PERSON><PERSON> du sicher, dass du diesen MCP-Server l<PERSON>schen möchtest?", "com_ui_delete_mcp_error": "Fehler beim Löschen des MCP-Servers", "com_ui_delete_mcp_success": "MCP-Server er<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_delete_memory": "Gedächtnis löschen", "com_ui_delete_prompt": "Prompt löschen?", "com_ui_delete_shared_link": "Geteilten Link löschen?", "com_ui_delete_tool": "Werkzeug löschen", "com_ui_delete_tool_confirm": "B<PERSON> du sicher, dass du dieses Werkzeug löschen möchtest?", "com_ui_deleted": "Gelöscht", "com_ui_descending": "Absteigend", "com_ui_description": "Beschreibung", "com_ui_description_placeholder": "Optional: Gib eine Beschreibung für den Prompt ein", "com_ui_deselect_all": "Alle abwählen", "com_ui_disabling": "Deaktiviere …", "com_ui_download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_download_artifact": "Artefakt herunterladen", "com_ui_download_backup": "Backup-<PERSON>s her<PERSON><PERSON><PERSON>n", "com_ui_download_backup_tooltip": "<PERSON><PERSON>, laden Sie bitte Ihre <PERSON>up-Codes herunter. <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> sie, um den Zugang wiederherzustellen, falls Sie Ihr Authentifizierungsgerät verlieren.", "com_ui_download_error": "Fe<PERSON> beim Herunterladen der Datei. Die Datei wurde möglicherweise gelöscht.", "com_ui_drag_drop": "Ziehen und Ablegen", "com_ui_dropdown_variables": "Dropdown-Variablen:", "com_ui_dropdown_variables_info": "<PERSON><PERSON><PERSON><PERSON> Sie benutzerdefinierte Dropdown-Menüs für Ihre Eingabeaufforderungen: `{{variable_name:option1|option2|option3}}`", "com_ui_duplicate": "Duplizieren", "com_ui_duplication_error": "<PERSON><PERSON>plizieren der Konversation ist ein Fehler aufgetreten", "com_ui_duplication_processing": "Konversation wird dupliziert...", "com_ui_duplication_success": "Unterhaltung erfolgreich dupliziert", "com_ui_edit": "<PERSON><PERSON><PERSON>", "com_ui_edit_editing_image": "Bild bearbeiten\n", "com_ui_edit_mcp_server": "MCP-Server bearbeiten", "com_ui_edit_memory": "Gedächtnis bearbeiten", "com_ui_empty_category": "-", "com_ui_endpoint": "Endpunkt", "com_ui_endpoint_menu": "LLM-Endpunkt-Menü", "com_ui_enter": "Eingabe", "com_ui_enter_api_key": "API-Schlüssel eingeben", "com_ui_enter_key": "Schlüssel eingeben", "com_ui_enter_openapi_schema": "Gib hier dein OpenAPI-<PERSON>hem<PERSON> ein", "com_ui_enter_value": "<PERSON><PERSON> e<PERSON>ben", "com_ui_error": "<PERSON><PERSON>", "com_ui_error_connection": "Verbindungsfehler zum Server. Versuche, die Seite zu aktualisieren.", "com_ui_error_save_admin_settings": "<PERSON><PERSON> S<PERSON>ichern Ihrer Admin-Einstellungen ist ein Fehler aufgetreten.", "com_ui_error_updating_preferences": "Fehler beim Aktualisieren der Einstellungen", "com_ui_examples": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_expand_chat": "<PERSON><PERSON> er<PERSON>tern", "com_ui_export_convo_modal": "Konversation exportieren", "com_ui_feedback_more": "Mehr ...", "com_ui_feedback_more_information": "Zusätzliches Feedback", "com_ui_feedback_negative": "Muss verbessert werden", "com_ui_feedback_placeholder": "<PERSON><PERSON><PERSON>e hier bitte weiteres Fe<PERSON>back an", "com_ui_feedback_positive": "<PERSON>rima, sehr gut", "com_ui_feedback_tag_attention_to_detail": "Liebe zum Detail", "com_ui_feedback_tag_bad_style": "Clear and Well-Written", "com_ui_feedback_tag_clear_well_written": "<PERSON><PERSON> und gut geschrieben", "com_ui_feedback_tag_creative_solution": "Kreative Lösung", "com_ui_feedback_tag_inaccurate": "Ungenaue oder falsche Antwort", "com_ui_feedback_tag_missing_image": "<PERSON><PERSON><PERSON> er<PERSON>", "com_ui_feedback_tag_not_helpful": "<PERSON>s fehlten nützliche Informationen", "com_ui_feedback_tag_not_matched": "Entspricht nicht der Anfrage", "com_ui_feedback_tag_other": "<PERSON><PERSON>", "com_ui_feedback_tag_unjustified_refusal": "Mi<PERSON> anderer Begründung abgelehnt", "com_ui_field_required": "<PERSON><PERSON> ist erford<PERSON>lich", "com_ui_file_size": "Dateigröße", "com_ui_files": "<PERSON><PERSON>\n", "com_ui_filter_prompts": "Prompts filtern", "com_ui_filter_prompts_name": "Prompts nach Namen filtern", "com_ui_final_touch": "<PERSON><PERSON><PERSON>\n", "com_ui_finance": "Fin<PERSON>zen", "com_ui_fork": "Abzweigen", "com_ui_fork_all_target": "Alle bis/von hier einbeziehen", "com_ui_fork_branches": "Zugehörige Verzweigungen einbeziehen", "com_ui_fork_change_default": "Standard-Abzweigungsoption", "com_ui_fork_default": "Standard-Abzweigungsoption verwenden", "com_ui_fork_error": "<PERSON><PERSON>weigen der Konversation ist ein Fehler aufgetreten", "com_ui_fork_from_message": "Wähle eine Abzweigungsoption", "com_ui_fork_info_1": "Verwende diese Einstellung, um Nachrichten mit dem gewünschten Verhalten abzuzweigen.", "com_ui_fork_info_2": "\"Abzweigen\" bezieht sich auf das Erstellen einer neuen Konversation, die von bestimmten Nachrichten in der aktuellen Konversation ausgeht/endet und eine Kopie gemäß den ausgewählten Optionen erstellt.", "com_ui_fork_info_3": "Die \"Zielnachricht\" bezieht sich entweder auf die Nachricht, von der aus dieses <PERSON>up geöffnet wurde, oder, wenn du \"{{0}}\" aktiv<PERSON><PERSON>, auf die letzte Nachricht in der Konversation.", "com_ui_fork_info_branches": "Diese Option zweigt die sichtbaren Nachrichten zusammen mit zugehörigen Verzweigungen ab; mit anderen W<PERSON>, den direkten Pfad zur Zielnachricht, einschließlich der Verzweigungen entlang des Pfades.", "com_ui_fork_info_button_label": "Informationen zum Abspalten von Chats anzeigen", "com_ui_fork_info_remember": "Aktiv<PERSON><PERSON> dies, um sich die von dir ausgewählten Optionen für zukünftige Verwendung zu merken, um das Abzweigen von Konversationen nach deinen Vorlieben zu beschleunigen.", "com_ui_fork_info_start": "<PERSON><PERSON> akt<PERSON>, beginnt das Abzweigen von dieser Nachricht bis zur letzten Nachricht in der Konversation, gemäß dem oben ausgewählten Verhalten.", "com_ui_fork_info_target": "Diese Option zweigt alle Nachrichten ab, die zur Zielnachricht führen, einsch<PERSON>ß<PERSON> ihrer Nachbarn; mit anderen W<PERSON>, alle Nachrichtenverzweigungen werden einbezogen, un<PERSON><PERSON><PERSON><PERSON><PERSON>, ob sie sichtbar sind oder sich auf demselben Pfad befinden.", "com_ui_fork_info_visible": "Diese Option zweigt nur die sichtbaren Nachrichten ab; mit anderen Worten, den direkten Pfad zur Zielnachricht, ohne jegliche Verzweigungen.", "com_ui_fork_more_details_about": "Zusätzliche Informationen und Details zur Abspaltungsoption '{{0}}' anzeigen", "com_ui_fork_more_info_options": "Detaillierte Erklärung aller Abspaltungsoptionen und ihres Verhaltens anzeigen", "com_ui_fork_processing": "Konversation wird abgezweigt...", "com_ui_fork_remember": "<PERSON><PERSON><PERSON>", "com_ui_fork_remember_checked": "<PERSON>hre Auswahl wird nach der Verwendung gespeichert. <PERSON> kannst dies jederzeit in den Einstellungen ändern.", "com_ui_fork_split_target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hier beginnen", "com_ui_fork_split_target_setting": "Abzweigung standardmäß<PERSON> von der Zielnachricht beginnen", "com_ui_fork_success": "Konversation erfolgreich abgezweigt", "com_ui_fork_visible": "<PERSON><PERSON> sichtbare Nachrichten", "com_ui_generate_backup": "Backup-<PERSON><PERSON> generieren", "com_ui_generate_qrcode": "QR-<PERSON> generieren", "com_ui_generating": "<PERSON><PERSON><PERSON> …", "com_ui_generation_settings": "Einstellungen für die Generierung", "com_ui_getting_started": "<PERSON><PERSON><PERSON>\n", "com_ui_global_group": "<PERSON><PERSON> – etwas fehlt noch", "com_ui_go_back": "Zurück", "com_ui_go_to_conversation": "Zur Konversation gehen", "com_ui_good_afternoon": "Guten Nachmittag", "com_ui_good_evening": "<PERSON><PERSON><PERSON>", "com_ui_good_morning": "<PERSON><PERSON><PERSON>", "com_ui_happy_birthday": "<PERSON><PERSON> ist mein 1. Geburtstag!", "com_ui_hide_image_details": "Details zum Bild ausblenden", "com_ui_hide_qr": "QR-Code ausblenden", "com_ui_host": "Host", "com_ui_icon": "Icon", "com_ui_idea": "Ideen", "com_ui_image_created": "Bild er<PERSON>llt\n", "com_ui_image_details": "Details zum Bild", "com_ui_image_edited": "Bild bearbei<PERSON>t\n", "com_ui_image_gen": "Bildgenerierung", "com_ui_import": "Importieren", "com_ui_import_conversation_error": "Beim Importieren Ihrer Konversationen ist ein Fehler aufgetreten", "com_ui_import_conversation_file_type_error": "Nicht unterstützter Importtyp", "com_ui_import_conversation_info": "Konversationen aus einer JSON-Datei importieren", "com_ui_import_conversation_success": "Konversationen erfolgreich importiert", "com_ui_include_shadcnui": "Anweisungen für shadcn/ui-Komponenten einschließen", "com_ui_input": "Eingabe", "com_ui_instructions": "Anweisungen", "com_ui_key": "Schlüssel", "com_ui_late_night": "Schöne späte Nacht", "com_ui_latest_footer": "Alle KIs für alle.", "com_ui_latest_production_version": "Neueste Produktiv-Version", "com_ui_latest_version": "Neueste Version", "com_ui_librechat_code_api_key": "Hole dir deinen LibreChat Code Interpreter API-Schlüssel", "com_ui_librechat_code_api_subtitle": "Sicher. Mehrsprachig. Ein-/Ausgabedateien.", "com_ui_librechat_code_api_title": "KI-Code ausführen", "com_ui_loading": "<PERSON>de …", "com_ui_locked": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_logo": "{{0}} Logo", "com_ui_manage": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_max_tags": "Die maximale Anzahl ist {{0}}, es werden die neuesten Werte verwendet.", "com_ui_mcp_dialog_desc": "<PERSON>te geben Si<PERSON> unten die erforderlichen Informationen ein.", "com_ui_mcp_enter_var": "<PERSON><PERSON><PERSON> Sie einen Wert für {{0}} ein", "com_ui_mcp_server_not_found": "Server nicht gefunden", "com_ui_mcp_servers": "MCP Server", "com_ui_mcp_url": "MCP-Server-URL", "com_ui_memories": "Gedächtnisse", "com_ui_memories_allow_create": "Erinnerungen erstellen erlauben", "com_ui_memories_allow_opt_out": "Benutzern erlauben, die Erinnerungsnutzung zu deaktivieren", "com_ui_memories_allow_read": "Erinnerungen lesen erlauben", "com_ui_memories_allow_update": "Erinnerungen aktualisieren erlauben", "com_ui_memories_allow_use": "Erinnerungen nutzen erlauben", "com_ui_memories_filter": "Erinnerungen filtern...", "com_ui_memory": "Erinnerung", "com_ui_memory_created": "Erinnerung erfolgreich erstellt", "com_ui_memory_deleted": "Erinnerung gelöscht", "com_ui_memory_deleted_items": "Gelöschte Erinnerungen", "com_ui_memory_key_exists": "Eine Erinnerung mit diesem Schlüssel existiert bereits. Bitte verwende einen anderen Schlüssel.", "com_ui_memory_updated": "Erinnerung aktualisiert", "com_ui_memory_updated_items": "Aktualisierte Erinnerungen", "com_ui_mention": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> einen Endpunkt, Assistenten oder eine Voreinstellung, um schnell dorthin zu wechseln", "com_ui_min_tags": "<PERSON>s können nicht mehr Werte entfernt werden, mindestens {{0}} sind erforderlich.", "com_ui_misc": "Verschiedenes", "com_ui_model": "KI-Modell", "com_ui_model_parameters": "Modell-Parameter", "com_ui_more_info": "Me<PERSON> Infos", "com_ui_my_prompts": "<PERSON><PERSON> Prompts", "com_ui_name": "Name", "com_ui_new": "<PERSON>eu", "com_ui_new_chat": "<PERSON><PERSON><PERSON>", "com_ui_new_conversation_title": "Neuer Titel des Chats", "com_ui_next": "<PERSON><PERSON>", "com_ui_no": "<PERSON><PERSON>", "com_ui_no_backup_codes": "<PERSON><PERSON>-Codes verfügbar. Bitte erstelle neue.", "com_ui_no_bookmarks": "Du hast noch keine Lesezeichen. Klicke auf einen Chat und füge ein neues hinzu", "com_ui_no_category": "<PERSON><PERSON>", "com_ui_no_changes": "Keine Änderungen zum Aktualisieren", "com_ui_no_data": "<PERSON><PERSON> – etwas fehlt noch", "com_ui_no_personalization_available": "Derzeit sind keine Personalisierungsoptionen verfügbar.", "com_ui_no_read_access": "Du hast keine Berechtigung, Erinnerungen anzuzeigen.", "com_ui_no_terms_content": "Keine Inhalte der Allgemeinen Geschäftsbedingungen zum Anzeigen", "com_ui_no_valid_items": "Leer - Text fehlt noch", "com_ui_none": "<PERSON><PERSON>", "com_ui_not_used": "<PERSON>cht verwendet", "com_ui_nothing_found": "Nichts gefunden", "com_ui_oauth": "OAuth", "com_ui_oauth_connected_to": "Verbunden mit", "com_ui_oauth_error_callback_failed": "Authentifizierungs Callback fehlgeschlagen. Bitte versuchen Sie es erneut.", "com_ui_oauth_error_generic": "Authentifizierung fehlgeschlagen. Bitte versuchen Sie es erneut.", "com_ui_oauth_error_invalid_state": "Ungültiger Statusparameter. Bitte versuchen Sie es erneut.", "com_ui_oauth_error_missing_code": "Autorisierungscode fehlt. Bitte versuchen Sie es erneut.", "com_ui_oauth_error_missing_state": "Statusparameter fehlt. Bitte versuchen Sie es erneut.", "com_ui_oauth_error_title": "Authentifizierung fehlgeschlagen", "com_ui_oauth_success_description": "Ihre Authentifizierung war erfolgreich. Dieses Fen<PERSON> schliesst sich in", "com_ui_oauth_success_title": "Authentifizierung erfolgreich", "com_ui_of": "von", "com_ui_off": "Aus", "com_ui_on": "An", "com_ui_openai": "OpenAI", "com_ui_optional": "(Optional)", "com_ui_page": "Seite", "com_ui_preferences_updated": "Einstellungen erfolgreich aktualisiert", "com_ui_prev": "Zurück", "com_ui_preview": "Vorschau", "com_ui_privacy_policy": "Datenschutzerklärung", "com_ui_privacy_policy_url": "Datenschutzrichtlinie-URL", "com_ui_prompt": "Prompt", "com_ui_prompt_already_shared_to_all": "Dieser Prompt wird bereits mit allen Benutzern geteilt", "com_ui_prompt_name": "Prompt-Name", "com_ui_prompt_name_required": "Prompt-Name ist er<PERSON><PERSON><PERSON>", "com_ui_prompt_preview_not_shared": "Der Autor hat die Zusammenarbeit für diesen Prompt nicht erlaubt.", "com_ui_prompt_text": "Text", "com_ui_prompt_text_required": "Text ist erforderlich", "com_ui_prompt_update_error": "Beim Aktualisieren des Prompts ist ein Fehler aufgetreten", "com_ui_prompts": "Prompts", "com_ui_prompts_allow_create": "Erstellung von Prompts erlauben", "com_ui_prompts_allow_share_global": "<PERSON><PERSON><PERSON> von Prompts mit allen Benutzern erlauben", "com_ui_prompts_allow_use": "<PERSON><PERSON><PERSON><PERSON><PERSON> von Prompts erlauben", "com_ui_provider": "<PERSON><PERSON><PERSON>", "com_ui_quality": "Qualität", "com_ui_read_aloud": "Vorlesen", "com_ui_redirecting_to_provider": "Weiterleitung zu {{0}}, einen <PERSON> bitte...", "com_ui_reference_saved_memories": "Gespeicherte Erinnerungen referenzieren", "com_ui_reference_saved_memories_description": "Erlaube dem Assistenten, deine gespeicherten Erinnerungen beim Antworten zu referenzieren und zu nutzen.", "com_ui_refresh_link": "Link aktualisieren", "com_ui_regenerate": "<PERSON><PERSON> gene<PERSON>", "com_ui_regenerate_backup": "Backup-Codes neu generieren", "com_ui_regenerating": "Generiere neu ...", "com_ui_region": "Region", "com_ui_rename": "Umbenennen", "com_ui_rename_conversation": "<PERSON><PERSON> umben<PERSON>n", "com_ui_rename_failed": "Chat konnte nicht umbenannt werden.", "com_ui_rename_prompt": "Prompt umbenennen", "com_ui_requires_auth": "Authentifizierung erforderlich", "com_ui_reset_var": "{{0}} <PERSON><PERSON><PERSON><PERSON>", "com_ui_reset_zoom": "Zoom zurücksetzen", "com_ui_result": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_revoke": "Widerrufen", "com_ui_revoke_info": "Benutzer-API-Keys widerrufen", "com_ui_revoke_key_confirm": "B<PERSON> du sicher, dass du diesen Schlüssel widerrufen möchtest?", "com_ui_revoke_key_endpoint": "API-Schlüssel für {{0}} widerrufen", "com_ui_revoke_keys": "Schlüssel widerrufen", "com_ui_revoke_keys_confirm": "B<PERSON> du sicher, dass du alle Schlüssel widerrufen möchtest?", "com_ui_role_select": "Rolle auswählen", "com_ui_roleplay": "Rollenspiel", "com_ui_run_code": "Code ausführen", "com_ui_run_code_error": "Bei der Ausführung des Codes ist ein Fehler aufgetreten", "com_ui_save": "Speichern", "com_ui_save_badge_changes": "Änderungen an Badges speichern?", "com_ui_save_submit": "Speichern & Absenden", "com_ui_saved": "Gespeichert!", "com_ui_saving": "Sicherung läuft...", "com_ui_schema": "<PERSON><PERSON><PERSON>", "com_ui_scope": "Umfang", "com_ui_search": "<PERSON><PERSON>", "com_ui_seconds": "Sekunden", "com_ui_secret_key": "Geheimschlüssel", "com_ui_select": "Auswählen", "com_ui_select_all": "Alle auswählen", "com_ui_select_file": "<PERSON>i ausw<PERSON>hlen", "com_ui_select_model": "Ein KI-Modell auswählen", "com_ui_select_provider": "<PERSON><PERSON><PERSON><PERSON> einen Anbieter", "com_ui_select_provider_first": "<PERSON><PERSON><PERSON>e zu<PERSON>t einen Anbieter", "com_ui_select_region": "Wähle eine Region", "com_ui_select_search_model": "KI-Modell nach Namen suchen", "com_ui_select_search_plugin": "<PERSON>lugin nach Namen suchen", "com_ui_select_search_provider": "Provider nach Name suchen", "com_ui_select_search_region": "Region nach Name suchen", "com_ui_share": "Teilen", "com_ui_share_create_message": "<PERSON>hr Name und alle Nachrichten, die du nach dem Teilen hinzufügst, bleiben privat.", "com_ui_share_delete_error": "Beim Löschen des geteilten Links ist ein Fehler aufgetreten", "com_ui_share_error": "<PERSON><PERSON> des Chat-Links ist ein Fehler aufgetreten", "com_ui_share_form_description": "Leer - Text fehlt noch", "com_ui_share_link_to_chat": "<PERSON> zum <PERSON> teilen", "com_ui_share_to_all_users": "<PERSON>t allen Benutzern teilen", "com_ui_share_update_message": "<PERSON><PERSON> <PERSON>, benutzerdefinierte Anweisungen und alle Nachrichten, die du nach dem Teilen hinzufügen, bleiben privat.", "com_ui_share_var": "{{0}} teilen", "com_ui_shared_link_bulk_delete_success": "Geteilte Links erfolgreich gel<PERSON>t", "com_ui_shared_link_delete_success": "Geteilter Link erfolgreich gelöscht", "com_ui_shared_link_not_found": "Geteilter Link nicht gefunden", "com_ui_shared_prompts": "Get<PERSON><PERSON> Prompts", "com_ui_shop": "Einkaufen", "com_ui_show": "Anzeigen", "com_ui_show_all": "Alle anzeigen", "com_ui_show_image_details": "Details zum Bild anzeigen", "com_ui_show_qr": "QR-Code anzeigen", "com_ui_sign_in_to_domain": "Anmelden bei {{0}}", "com_ui_simple": "<PERSON><PERSON><PERSON>", "com_ui_size": "Größe", "com_ui_special_var_current_date": "Aktuelles Datum", "com_ui_special_var_current_datetime": "Aktuelles Datum & Uhrzeit", "com_ui_special_var_current_user": "Aktueller Nutzer", "com_ui_special_var_iso_datetime": "UTC ISO Datum/Zeit", "com_ui_special_variables": "Spezielle Variablen:", "com_ui_special_variables_more_info": "Du kannst spezielle Variablen aus den Dropdown-<PERSON><PERSON><PERSON> au<PERSON>: `{{current_date}}` (heutiges Datum und Wochentag), `{{current_datetime}}` (offizielles Datum und Uhrzeit), `{{utc_iso_datetime}}` (UTC ISO Datum/Zeit) und `{{current_user}}` (dein <PERSON>ntoname).", "com_ui_speech_while_submitting": "Spracheingabe nicht möglich während eine Antwort generiert wird", "com_ui_sr_actions_menu": "Aktionsmenü für \"{{0}}\" öffnen", "com_ui_stop": "Stopp", "com_ui_storage": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_submit": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_teach_or_explain": "<PERSON><PERSON><PERSON>", "com_ui_temporary": "Privater <PERSON><PERSON>", "com_ui_terms_and_conditions": "Allgemeine Geschäftsbedingungen", "com_ui_terms_of_service": "Nutzungsbedingungen", "com_ui_thinking": "Nachdenken...", "com_ui_thoughts": "Gedanken", "com_ui_token": "Token", "com_ui_token_exchange_method": "Token-Austauschmethode", "com_ui_token_url": "Token-URL", "com_ui_tokens": "Tokens", "com_ui_tool_collection_prefix": "<PERSON><PERSON> Tools Sammlung von", "com_ui_tool_info": "Tool Information", "com_ui_tool_more_info": "Mehr Information über dieses Tool", "com_ui_tools": "Werkzeuge", "com_ui_travel": "<PERSON><PERSON><PERSON>", "com_ui_trust_app": "<PERSON>ch vertraue dieser <PERSON>", "com_ui_unarchive": "Aus Archiv holen", "com_ui_unarchive_error": "Konversation konnte nicht aus dem Archiv geholt werden", "com_ui_unknown": "Unbekannt", "com_ui_untitled": "Unbenannt", "com_ui_update": "Aktualisieren", "com_ui_update_mcp_error": "<PERSON><PERSON>rstellen oder Aktualisieren des MCP ist ein Fehler aufgetreten.", "com_ui_update_mcp_success": "MCP erfolgreich erstellt oder aktualisiert", "com_ui_upload": "Hochladen", "com_ui_upload_code_files": "Hochladen für Code-Interpreter", "com_ui_upload_delay": "<PERSON> Hochladen von \"{{0}}\" dauert etwas länger. <PERSON>te warte, während die Datei für den Abruf indexiert wird.", "com_ui_upload_error": "<PERSON><PERSON> Ihrer Datei ist ein Fehler aufgetreten", "com_ui_upload_file_context": "Kontext der Datei hochladen", "com_ui_upload_file_search": "Hochladen für Dateisuche", "com_ui_upload_files": "<PERSON><PERSON>", "com_ui_upload_image": "Ein Bild hochladen", "com_ui_upload_image_input": "Bild hochladen", "com_ui_upload_invalid": "Ungültige Datei zum Hochladen. Muss ein Bild sein und das Limit nicht überschreiten", "com_ui_upload_invalid_var": "Ungültige Datei zum Hochladen. Muss ein Bild sein und {{0}} MB nicht überschreiten", "com_ui_upload_ocr_text": "Hochladen als Text", "com_ui_upload_success": "Datei erfolgreich hoch<PERSON>aden", "com_ui_upload_type": "Upload-Typ auswählen", "com_ui_usage": "<PERSON><PERSON>ung", "com_ui_use_2fa_code": "Stattdessen 2FA-Code verwenden", "com_ui_use_backup_code": "Stattdessen Backup-Code verwenden", "com_ui_use_memory": "Erinnerung nutzen", "com_ui_use_micrphone": "Mikrofon verwenden", "com_ui_use_prompt": "Prompt verwenden", "com_ui_used": "Verwendet", "com_ui_value": "Wert", "com_ui_variables": "Variablen", "com_ui_variables_info": "Verwende doppelte geschweifte Klammern in Ihrem Text, um Variablen zu erstellen, z.B. {{Beispielvariable}}, die du später beim Verwenden des Prompts ausfüllen kannst.", "com_ui_verify": "Überprüfen", "com_ui_version_var": "Version {{0}}", "com_ui_versions": "<PERSON>en", "com_ui_view_memory": "Erinnerung anzeigen", "com_ui_view_source": "Quell-<PERSON><PERSON> anzeigen", "com_ui_web_search": "Web-Suche\n", "com_ui_web_search_cohere_key": "Cohere API-Key eingeben", "com_ui_web_search_firecrawl_url": "Firecrawl API URL (optional)\n", "com_ui_web_search_jina_key": "Den Jina API Schlüssel eingeben", "com_ui_web_search_processing": "Ergebnisse verarbeiten", "com_ui_web_search_provider": "<PERSON><PERSON><PERSON> der Suche", "com_ui_web_search_provider_serper": "Serper API\n", "com_ui_web_search_provider_serper_key": "<PERSON>en Serper API Schlüssel holen", "com_ui_web_search_reading": "Lesen der Suchergebnisse", "com_ui_web_search_reranker": "<PERSON><PERSON><PERSON>", "com_ui_web_search_reranker_cohere": "Cohere", "com_ui_web_search_reranker_cohere_key": "Einen Cohere API-Schl<PERSON>ssel holen", "com_ui_web_search_reranker_jina": "Jina AI\n", "com_ui_web_search_reranker_jina_key": "Einen Jina API Schlüssel holen", "com_ui_web_search_scraper": "<PERSON><PERSON><PERSON>", "com_ui_web_search_scraper_firecrawl": "Firecrawl API\n", "com_ui_web_search_scraper_firecrawl_key": "Einen Firecrawl API Schlüssel holen", "com_ui_web_searching": "Internetsuche läuft", "com_ui_web_searching_again": "Sucht erneut im Internet", "com_ui_weekend_morning": "Schönes Wochenende", "com_ui_write": "Schreiben", "com_ui_x_selected": "{{0}} ausgewählt", "com_ui_yes": "<PERSON>a", "com_ui_zoom": "Zoom", "com_user_message": "<PERSON>"}